# Cập nhật Chat Model để khớp với Database

## 🎯 Mục tiêu
Sửa lại code chat để khớp với database model thực tế:

### Database Model (Message table):
- **Id**: randomString
- **DateCreated**: Ng<PERSON>y tạo dạng gettime (timestamp)
- **Content**: Nội dung tin nhắn
- **Type**: loại tin nhắn (1-text, 2-ảnh, 3-File, 4-Emoji)
- **FileUrl**: string để lưu file ảnh, audio, file...
- **CustomerId**: người gửi
- **ChatRoomId**: ID phòng chat

## ✅ Những thay đổi đã thực hiện

### 1. Cập nhật ChatMessage Interface (`src/modules/chat/types/ChatTypes.ts`)
```typescript
export interface ChatMessage {
  Id: string;                    // randomString
  DateCreated: number;           // Ngày tạo dạng gettime
  Content: string;               // Nội dung tin nhắn
  Type: string;                  // loại tin nhắn: 1-text, 2-ảnh, 3-File, 4-Emoji
  FileUrl?: string;              // dạng string để lưu các file ảnh, audio, file
  CustomerId: string;            // người gửi
  ChatRoomId: string;            // ID phòng chat
  
  // Additional fields for UI
  user?: ChatUser;               // Thông tin user (populated from CustomerId)
  sent?: boolean;                // Trạng thái gửi
  received?: boolean;            // Trạng thái nhận
}
```

### 2. Tạo MessageConverter Utility (`src/modules/chat/utils/MessageConverter.ts`)
- **toGiftedChatFormat()**: Chuyển đổi từ database format sang GiftedChat format
- **toDatabaseFormat()**: Chuyển đổi từ GiftedChat format sang database format
- **toGiftedChatArray()**: Chuyển đổi array messages
- Các helper methods: isEmoji(), hasFileAttachment(), getFileExtension(), etc.

### 3. Cập nhật ChatRoomScreen (`src/modules/chat/screens/ChatRoomScreen.tsx`)

#### Message Conversion:
```typescript
// Sử dụng MessageConverter thay vì manual mapping
const giftedChatMessages = MessageConverter.toGiftedChatArray(messages);
```

#### onSend Function:
```typescript
const onSend = useCallback(async (newMessages: any[]) => {
  const message = newMessages[0];
  
  // Convert GiftedChat message to database format
  const dbMessageData = MessageConverter.toDatabaseFormat(
    message,
    customer?.Id || customer?.id || '',
    roomId
  );
  
  // Create complete ChatMessage with user info
  const chatMessage: ChatMessage = {
    ...dbMessageData,
    user: { /* user info */ },
    sent: true,
    received: false,                    
  } as ChatMessage;
  
  // Send to store, socket, and API
}, [roomId, customer, dispatch]);
```

#### Socket Message Handling:
```typescript
SocketService.onReceiveMessage((data) => {
  const chatMessage: ChatMessage = {
    Id: message.Id || Date.now().toString(),
    Content: message.Content || message.text || message,
    DateCreated: message.DateCreated || new Date().getTime(),
    Type: message.Type || '1', // Default to text
    FileUrl: message.FileUrl,
    CustomerId: fromUserId,
    ChatRoomId: messageRoomId,
    // ...
  };
});
```

#### Message Type Handling:
- **Type '1'**: Text messages
- **Type '2'**: Image messages (hiển thị image trong bubble)
- **Type '3'**: File messages (hiển thị file attachment)
- **Type '4'**: Emoji messages

#### Enhanced Bubble Rendering:
```typescript
const renderBubble = (props: any) => (
  <Bubble
    {...props}
    renderMessageImage={/* Image handling */}
    renderCustomView={/* File attachment handling */}
  />
);
```

### 4. Emoji Handling
```typescript
const handleEmojiSelect = (emoji: string) => {
  if (inputText.trim() === '') {
    // Send emoji as separate message (Type '4')
    const emojiMessage = { /* emoji message */ };
    onSend([emojiMessage]);
  } else {
    // Add emoji to current input text
    setInputText(prev => prev + emoji);
  }
};
```

### 5. Image Message Handling
```typescript
const sendImageMessage = async (image: any) => {
  // Upload image first
  const imageUrl = await ChatAPI.uploadChatFile(image, 'image');
  
  // Create GiftedChat format message with image
  const giftedMessage = {
    _id: Date.now().toString(),
    text: '',
    image: imageUrl, // Will be converted to Type '2' and FileUrl
    // ...
  };
  
  onSend([giftedMessage]);
};
```

## 🔄 Message Flow

### Gửi tin nhắn:
1. User nhập text/chọn emoji/chọn ảnh trong GiftedChat
2. `onSend()` được gọi với GiftedChat format
3. `MessageConverter.toDatabaseFormat()` chuyển đổi sang database format
4. Lưu vào Redux store, gửi qua Socket và API

### Nhận tin nhắn:
1. Nhận từ API hoặc Socket với database format
2. Lưu vào Redux store với database format
3. `MessageConverter.toGiftedChatArray()` chuyển đổi sang GiftedChat format để hiển thị

### Hiển thị tin nhắn:
1. Messages từ Redux store (database format)
2. Convert sang GiftedChat format
3. GiftedChat render theo type:
   - Type '1': Text bubble
   - Type '2': Image bubble
   - Type '3': File attachment
   - Type '4': Emoji text

## 📝 Lưu ý quan trọng

1. **Database format** được sử dụng cho:
   - Redux store
   - API calls
   - Socket communication

2. **GiftedChat format** chỉ được sử dụng cho:
   - UI rendering
   - GiftedChat component props

3. **MessageConverter** đảm bảo:
   - Chuyển đổi chính xác giữa 2 formats
   - Xử lý đúng các message types
   - Maintain data integrity

4. **Type mapping**:
   - '1' = Text
   - '2' = Image (FileUrl chứa image URL)
   - '3' = File (FileUrl chứa file URL, Content chứa filename)
   - '4' = Emoji (Content chứa emoji)

## 🚀 Kết quả

- ✅ Chat messages khớp hoàn toàn với database model
- ✅ Hỗ trợ đầy đủ các loại tin nhắn (text, image, file, emoji)
- ✅ Chuyển đổi tự động giữa database và UI formats
- ✅ Maintain backward compatibility
- ✅ Clean code architecture với MessageConverter utility
