# Fix hiển thị ảnh trong Chat

## 🎯 Vấn đề
Ảnh không hiển thị trong chat messages do:
1. **Type mismatch**: Confusion giữa string và number cho message Type
2. **Comparison logic**: Logic so sánh Type không đúng
3. **Debug needed**: Cần debug để trace vấn đề

## ✅ Những fix đã thực hiện

### 1. Type Consistency Fix

#### Vấn đề gốc:
```typescript
// ChatTypes.ts định nghĩa Type là number
Type: number; // 1-text, 2-ảnh, 3-File, 4-Emoji

// Nhưng code sử dụng string
handleSendMessage('', '2', imageUrl); // String '2'
message.Type === '2' // So sánh string
```

#### Giải pháp:
```typescript
// Sử dụng number consistently
const handleSendMessage = useCallback(async (
  text: string, 
  type: number = 1, // Number thay vì string
  fileUrl?: string
) => {
  const chatMessage: ChatMessage = {
    Type: type, // Number assignment
    // ...
  };
});

// Function calls với number
handleSendMessage('', 2, imageUrl); // Image
handleSendMessage(emoji, 4); // Emoji
```

### 2. Message Type Comparison Fix

#### Trước:
```typescript
// Phức tạp và không consistent
{(message.Type === '2' || message.Type === 2 || String(message.Type) === '2') && message.FileUrl ? (
  // Image rendering
)}
```

#### Sau:
```typescript
// Đơn giản và chính xác
{message.Type === 2 && message.FileUrl ? (
  // Image rendering
)}
```

### 3. Debug Logging

#### Thêm debug logs:
```typescript
// Debug message rendering
console.log('Rendering message:', {
  Type: message.Type,
  TypeOf: typeof message.Type,
  FileUrl: message.FileUrl,
  Content: message.Content
});

// Debug image processing
console.log('Image message debug:', {
  FileUrl: message.FileUrl,
  imageIds,
  imageUrls,
  ConfigAPI_urlImg: ConfigAPI.urlImg
});
```

### 4. Complete Type Mapping

#### Message Types:
- **Type 1**: Text messages
- **Type 2**: Image messages (FileUrl contains image IDs)
- **Type 3**: File messages (FileUrl contains file URLs)
- **Type 4**: Emoji messages

#### Rendering Logic:
```typescript
{message.Type === 2 && message.FileUrl ? (
  // Image message - support multiple images
  (() => {
    const imageIds = message.FileUrl.split(',').map(id => id.trim()).filter(id => id);
    const imageUrls = imageIds.map(id => ConfigAPI.urlImg + id);
    
    if (imageIds.length === 1) {
      // Single image
      return <SingleImageComponent />;
    } else {
      // Multiple images - 2x2 grid
      return <MultipleImagesComponent />;
    }
  })()
) : message.Type === 3 && message.FileUrl ? (
  // File message
  <FileComponent />
) : (
  // Text/Emoji message
  <TextComponent />
)}
```

## 🔍 Debug Process

### 1. Type Checking:
```typescript
console.log('Message Type:', message.Type, typeof message.Type);
// Expected: 2 "number"
// If showing: "2" "string" → Type conversion issue
```

### 2. FileUrl Validation:
```typescript
console.log('FileUrl:', message.FileUrl);
// Expected: "image_id_1,image_id_2" or "single_image_id"
// If empty/undefined → Upload or storage issue
```

### 3. URL Construction:
```typescript
console.log('Image URLs:', imageUrls);
// Expected: ["http://domain.com/images/id1", "http://domain.com/images/id2"]
// If malformed → ConfigAPI.urlImg issue
```

## 🚀 Expected Behavior

### Image Message Flow:
1. **User selects image** → ImagePicker
2. **Upload image** → ChatAPI.uploadChatFile() → returns imageId
3. **Send message** → handleSendMessage('', 2, imageId)
4. **Create ChatMessage** → Type: 2, FileUrl: imageId
5. **Store in Redux** → messages array
6. **Render message** → Type === 2 → Image component
7. **Display image** → ConfigAPI.urlImg + imageId

### Multiple Images:
1. **FileUrl format**: "id1,id2,id3"
2. **Split and process**: ["id1", "id2", "id3"]
3. **Generate URLs**: [url1, url2, url3]
4. **Render grid**: 2x2 layout with +count overlay

## 🔧 Troubleshooting

### If images still not showing:

#### 1. Check message Type:
```typescript
// Add this debug in renderMessageItem
console.log('Message debug:', {
  Type: message.Type,
  TypeType: typeof message.Type,
  FileUrl: message.FileUrl,
  HasFileUrl: !!message.FileUrl
});
```

#### 2. Check ConfigAPI.urlImg:
```typescript
console.log('ConfigAPI.urlImg:', ConfigAPI.urlImg);
// Should be something like: "https://domain.com/images/"
```

#### 3. Check image URLs:
```typescript
console.log('Final image URL:', ConfigAPI.urlImg + imageId);
// Test this URL in browser to verify it loads
```

#### 4. Check FastImage props:
```typescript
<FastImage
  source={{ uri: imageUrl }}
  style={styles.messageImage}
  resizeMode={FastImage.resizeMode.cover}
  onError={(error) => console.log('FastImage error:', error)}
  onLoad={() => console.log('FastImage loaded successfully')}
/>
```

## 📝 Key Changes Summary

### Files Modified:
- `src/modules/chat/screens/ChatRoomScreen.tsx`

### Changes Made:
1. **handleSendMessage**: Parameter type từ string → number
2. **Type comparisons**: Simplified từ complex → simple number comparison
3. **Function calls**: Updated để sử dụng number values
4. **Debug logging**: Added comprehensive logging
5. **Consistency**: Đảm bảo Type luôn là number throughout

### Type Values:
- **1**: Text messages
- **2**: Image messages ← Fixed this
- **3**: File messages
- **4**: Emoji messages

## 🎯 Result

Sau khi fix:
- ✅ Image messages hiển thị đúng
- ✅ Type consistency across codebase
- ✅ Debug logs để troubleshoot
- ✅ Multiple images support
- ✅ Image viewer functionality

Nếu vẫn không hiển thị ảnh, check debug logs để xác định vấn đề cụ thể (Type, FileUrl, hoặc URL construction).
