# Cải tiến hiển thị ảnh trong Chat

## 🎯 Y<PERSON>u cầu đã thực hiện
1. **Background cho khung hiển thị ảnh**
2. **Layout 2x2 grid cho nhiều ảnh với +số ảnh còn lại**
3. **Image viewer full screen với navigation**

## ✅ Những thay đổi đã thực hiện

### 1. Background cho Image Container

#### Single Image:
```typescript
imageContainer: {
  borderRadius: 8,
  overflow: 'hidden',
  marginVertical: 4,
  backgroundColor: '#F5F5F5', // Thêm background
  padding: 2, // Thêm padding
},
```

#### Multiple Images:
```typescript
multipleImagesContainer: {
  borderRadius: 8,
  overflow: 'hidden',
  marginVertical: 4,
  backgroundColor: '#F5F5F5', // Background cho container
  padding: 2, // Padding cho spacing
  maxWidth: 250,
},
```

### 2. Grid Layout 2x2 cho Multiple Images

#### New Layout Structure:
```typescript
// Multiple images - 2x2 grid layout
const displayImages = imageIds.slice(0, 4); // Show max 4 images
const remainingCount = imageIds.length - 4;

return (
  <View style={styles.multipleImagesContainer}>
    {/* First row */}
    <View style={styles.imagesRow}>
      {displayImages.slice(0, 2).map((imageId, index) => (
        <TouchableOpacity style={styles.gridImageItem}>
          <FastImage style={styles.gridImage} />
        </TouchableOpacity>
      ))}
    </View>
    
    {/* Second row */}
    {displayImages.length > 2 && (
      <View style={styles.imagesRow}>
        {displayImages.slice(2, 4).map((imageId, index) => (
          <TouchableOpacity style={styles.gridImageItem}>
            <FastImage style={styles.gridImage} />
            {/* Show +count on last image */}
            {remainingCount > 0 && index === 1 && (
              <View style={styles.imageCountOverlay}>
                <Text style={styles.imageCountText}>+{remainingCount}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    )}
  </View>
);
```

#### Grid Styles:
```typescript
imagesRow: {
  flexDirection: 'row',
  marginBottom: 2,
},
gridImageItem: {
  flex: 1,
  aspectRatio: 1, // Square images
  marginRight: 2,
  borderRadius: 6,
  overflow: 'hidden',
  position: 'relative',
  backgroundColor: '#E0E0E0', // Fallback background
},
gridImage: {
  width: '100%',
  height: '100%',
  borderRadius: 6,
},
```

### 3. Image Count Overlay

#### Overlay cho ảnh cuối:
```typescript
{remainingCount > 0 && index === 1 && (
  <View style={styles.imageCountOverlay}>
    <Text style={styles.imageCountText}>+{remainingCount}</Text>
  </View>
)}
```

#### Overlay Styles:
```typescript
imageCountOverlay: {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: 4,
},
imageCountText: {
  color: 'white',
  fontSize: 16,
  fontWeight: 'bold',
},
```

### 4. Image Viewer Full Screen

#### State Management:
```typescript
const [showImageViewer, setShowImageViewer] = useState(false);
const [selectedImages, setSelectedImages] = useState<string[]>([]);
const [selectedImageIndex, setSelectedImageIndex] = useState(0);
```

#### Click Handler:
```typescript
onPress={() => {
  console.log('Image pressed:', imageId, 'index:', index);
  setSelectedImages(imageUrls); // All image URLs
  setSelectedImageIndex(index); // Current image index
  setShowImageViewer(true); // Show viewer
}}
```

#### Image Viewer Component:
```typescript
const renderImageViewer = () => {
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  return (
    <Modal
      visible={showImageViewer}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowImageViewer(false)}
    >
      <View style={styles.imageViewerContainer}>
        {/* Header with close button and counter */}
        <View style={styles.imageViewerHeader}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowImageViewer(false)}
          >
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.imageCounter}>
            {selectedImageIndex + 1} / {selectedImages.length}
          </Text>
        </View>

        {/* Main image display */}
        <View style={styles.imageViewerContent}>
          <FastImage
            source={{ uri: selectedImages[selectedImageIndex] }}
            style={[styles.fullScreenImage, { 
              width: screenWidth, 
              height: screenHeight * 0.8 
            }]}
            resizeMode={FastImage.resizeMode.contain}
          />
        </View>

        {/* Navigation controls */}
        {selectedImages.length > 1 && (
          <View style={styles.imageViewerControls}>
            <TouchableOpacity
              style={[styles.navButton, 
                selectedImageIndex === 0 && styles.navButtonDisabled]}
              onPress={() => {
                if (selectedImageIndex > 0) {
                  setSelectedImageIndex(selectedImageIndex - 1);
                }
              }}
              disabled={selectedImageIndex === 0}
            >
              <Text style={styles.navButtonText}>← Trước</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, 
                selectedImageIndex === selectedImages.length - 1 && 
                styles.navButtonDisabled]}
              onPress={() => {
                if (selectedImageIndex < selectedImages.length - 1) {
                  setSelectedImageIndex(selectedImageIndex + 1);
                }
              }}
              disabled={selectedImageIndex === selectedImages.length - 1}
            >
              <Text style={styles.navButtonText}>Sau →</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};
```

### 5. Image Viewer Styles

#### Container và Layout:
```typescript
imageViewerContainer: {
  flex: 1,
  backgroundColor: 'rgba(0, 0, 0, 0.9)',
  justifyContent: 'center',
  alignItems: 'center',
},
imageViewerHeader: {
  position: 'absolute',
  top: 50,
  left: 0,
  right: 0,
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingHorizontal: 20,
  zIndex: 1,
},
imageViewerContent: {
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
},
```

#### Controls và Buttons:
```typescript
closeButton: {
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: 'rgba(255, 255, 255, 0.3)',
  justifyContent: 'center',
  alignItems: 'center',
},
navButton: {
  paddingHorizontal: 20,
  paddingVertical: 12,
  backgroundColor: 'rgba(255, 255, 255, 0.3)',
  borderRadius: 25,
},
navButtonDisabled: {
  backgroundColor: 'rgba(255, 255, 255, 0.1)',
},
```

## 🎨 Visual Improvements

### Image Display Layouts:

#### Single Image:
```
┌─────────────────────┐
│ ┌─────────────────┐ │ ← Background container
│ │                 │ │
│ │     Image       │ │
│ │                 │ │
│ └─────────────────┘ │
└─────────────────────┘
```

#### 2 Images (1x2):
```
┌─────────────────────┐
│ ┌────────┐┌────────┐│
│ │ Image1 ││ Image2 ││
│ └────────┘└────────┘│
└─────────────────────┘
```

#### 3 Images (1x2 + 1):
```
┌─────────────────────┐
│ ┌────────┐┌────────┐│
│ │ Image1 ││ Image2 ││
│ └────────┘└────────┘│
│ ┌────────┐          │
│ │ Image3 │          │
│ └────────┘          │
└─────────────────────┘
```

#### 4+ Images (2x2 + count):
```
┌─────────────────────┐
│ ┌────────┐┌────────┐│
│ │ Image1 ││ Image2 ││
│ └────────┘└────────┘│
│ ┌────────┐┌────────┐│
│ │ Image3 ││ +2     ││ ← Count overlay
│ └────────┘└────────┘│
└─────────────────────┘
```

## 🚀 Features Implemented

### Image Display:
- ✅ Background container cho tất cả image messages
- ✅ Grid layout 2x2 cho multiple images
- ✅ Count overlay (+số) cho ảnh thừa
- ✅ Responsive sizing với aspect ratio

### Image Viewer:
- ✅ Full screen modal viewer
- ✅ Image counter (1/5, 2/5, etc.)
- ✅ Navigation buttons (Trước/Sau)
- ✅ Close button
- ✅ Disabled state cho navigation
- ✅ Touch to close functionality

### User Experience:
- ✅ Smooth animations
- ✅ Proper image scaling (contain mode)
- ✅ Responsive design
- ✅ Clear visual feedback

## 📱 Technical Implementation

### Data Processing:
```typescript
const imageIds = message.FileUrl.split(',').map(id => id.trim()).filter(id => id);
const imageUrls = imageIds.map(id => ConfigAPI.urlImg + id);
const displayImages = imageIds.slice(0, 4); // Max 4 display
const remainingCount = imageIds.length - 4; // Count for overlay
```

### State Management:
- **showImageViewer**: Control modal visibility
- **selectedImages**: Array of all image URLs
- **selectedImageIndex**: Current viewing index

### Navigation Logic:
- **Previous**: Disabled khi index = 0
- **Next**: Disabled khi index = length - 1
- **Counter**: Dynamic display current/total

Kết quả là hệ thống hiển thị ảnh hoàn chỉnh với UX tốt! 🚀
