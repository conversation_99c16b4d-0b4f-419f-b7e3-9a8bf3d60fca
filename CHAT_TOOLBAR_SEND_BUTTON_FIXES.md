# Fix Toolbar và Send Button trong Chat UI

## 🎯 Vấn đề cần sửa
1. **Send button không hiển thị** khi gõ tin nhắn
2. **Toolbar phải nằm bên trái** ô input
3. **Logic focus/unfocus** cho toolbar hiển thị

## ✅ Những thay đổi đã thực hiện

### 1. Toolbar Layout Mới

#### Vị trí Toolbar:
```typescript
// TRƯỚC - Toolbar ở trên input
{showToolbar && (
  <View style={styles.toolbarRow}>
    {/* Toolbar buttons */}
  </View>
)}
<View style={styles.inputRow}>
  {/* Input */}
</View>

// SAU - Toolbar bên trái input
<View style={styles.inputRow}>
  <View style={styles.leftToolbarContainer}>
    {/* Toolbar logic */}
  </View>
  <View style={styles.inputContainer}>
    {/* Input */}
  </View>
  {/* Send button */}
</View>
```

### 2. Smart Toolbar Logic

#### 3 Trạng thái Toolbar:

##### A. Default State (không focus, toolbar đóng):
```typescript
{!isInputFocused && !showToolbar ? (
  // Hiển thị toolbar icons bên trái
  <>
    <TouchableOpacity onPress={handleLibraryPicker}>🖼️</TouchableOpacity>
    <TouchableOpacity onPress={handleCameraPicker}>📷</TouchableOpacity>
    <TouchableOpacity onPress={handleFilePicker}>📎</TouchableOpacity>
  </>
) : ...}
```

##### B. Focused State (đang focus input):
```typescript
{isInputFocused ? (
  // Hiển thị nút mở rộng
  <TouchableOpacity onPress={() => setShowToolbar(true)}>
    <Text>{'>'}</Text>
  </TouchableOpacity>
) : ...}
```

##### C. Expanded State (toolbar mở rộng):
```typescript
{showToolbar ? (
  // Hiển thị full toolbar + nút đóng
  <>
    <TouchableOpacity onPress={handleLibraryPicker}>🖼️</TouchableOpacity>
    <TouchableOpacity onPress={handleCameraPicker}>📷</TouchableOpacity>
    <TouchableOpacity onPress={handleFilePicker}>📎</TouchableOpacity>
    <TouchableOpacity onPress={() => setShowToolbar(false)}>
      <Text>{'<'}</Text>
    </TouchableOpacity>
  </>
) : null}
```

### 3. Input Container Responsive

#### Dynamic Width:
```typescript
<View style={[
  styles.inputContainer,
  (isInputFocused || showToolbar) && styles.inputContainerCompact
]}>
```

#### Styles:
```typescript
inputContainer: {
  flex: 1, // Full width khi toolbar đóng
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: '#F5F5F5',
  borderRadius: 20,
  borderWidth: 1,
  borderColor: ColorThemes.light.neutral_border_color,
  paddingRight: 8,
},
inputContainerCompact: {
  flex: 0.7, // Thu nhỏ khi toolbar mở rộng
},
```

### 4. Send Button Fix

#### Hiển thị Logic:
```typescript
// TRƯỚC - Hiển thị khi có text HOẶC focus
{(inputText.trim() || isInputFocused) && (
  <TouchableOpacity style={styles.sendButton}>
    <Text>➤</Text>
  </TouchableOpacity>
)}

// SAU - Chỉ hiển thị khi có text
{inputText.trim() && (
  <TouchableOpacity 
    style={styles.sendButton}
    onPress={() => {
      if (inputText.trim()) {
        handleSendMessage(inputText);
      }
    }}
  >
    <Text style={styles.sendIcon}>➤</Text>
  </TouchableOpacity>
)}
```

#### Benefits:
- **Rõ ràng**: Chỉ hiện khi thực sự có thể gửi
- **UX tốt**: Không confuse user
- **Performance**: Ít re-render

### 5. Focus/Blur Handling

#### Simplified Logic:
```typescript
onFocus={() => {
  setIsInputFocused(true);
  // Không tự động ẩn/hiện toolbar
}}
onBlur={() => {
  setIsInputFocused(false);
  // Không tự động ẩn/hiện toolbar
}}
```

#### Input Text Change:
```typescript
const handleInputTextChange = (text: string) => {
  setInputText(text);
  
  // Chỉ gửi typing indicator
  if (text.length > 0) {
    SocketService.sendTyping(roomId);
  }
  // Không can thiệp vào toolbar state
};
```

### 6. State Management

#### Initial State:
```typescript
const [showToolbar, setShowToolbar] = useState(false); // Mặc định đóng
const [isInputFocused, setIsInputFocused] = useState(false);
```

#### State Transitions:
- **Default**: `showToolbar: false, isInputFocused: false` → Hiện toolbar icons
- **Focus**: `showToolbar: false, isInputFocused: true` → Hiện nút expand (>)
- **Expand**: `showToolbar: true, isInputFocused: any` → Hiện full toolbar + nút collapse (<)

### 7. Layout Structure

#### New Layout:
```
┌─────────────────────────────────────────────────────┐
│ Input Toolbar Container                             │
│ ┌─────────────────────────────────────────────────┐ │
│ │ Input Row                                       │ │
│ │ ┌──────────┐ ┌─────────────────┐ ┌──────────┐  │ │
│ │ │ Left     │ │ Input Container │ │ Send     │  │ │
│ │ │ Toolbar  │ │ ┌─────────────┐ │ │ Button   │  │ │
│ │ │ Icons    │ │ │ TextInput   │ │ │ (if text)│  │ │
│ │ │ or       │ │ │ + Emoji     │ │ │          │  │ │
│ │ │ Toggle   │ │ └─────────────┘ │ │          │  │ │
│ │ └──────────┘ └─────────────────┘ └──────────┘  │ │
│ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘
```

### 8. Responsive Behavior

#### Screen States:

##### Default (không focus):
```
[🖼️] [📷] [📎] [────── Input với 😊 ──────]
```

##### Focused (đang gõ):
```
[>] [────── Input với 😊 ──────] [➤]
    (nếu có text)
```

##### Expanded (toolbar mở):
```
[🖼️] [📷] [📎] [<] [─── Input 😊 ───] [➤]
                    (compact)    (nếu có text)
```

## 🎨 Visual Improvements

### Before vs After:

#### Before:
- Send button không hiện khi gõ
- Toolbar ở trên input
- Logic focus/blur phức tạp

#### After:
- Send button hiện rõ ràng khi có text
- Toolbar bên trái input, responsive
- Logic đơn giản, UX tốt

### Key Features:
1. **Smart Layout**: Toolbar responsive theo trạng thái
2. **Clear Send Button**: Chỉ hiện khi có thể gửi
3. **Intuitive UX**: Dễ hiểu và sử dụng
4. **Compact Design**: Tối ưu không gian

## 📱 User Experience Flow

### Typing Flow:
1. **Mở app** → Thấy toolbar icons bên trái
2. **Tap input** → Toolbar icons biến thành nút expand (>)
3. **Gõ text** → Send button xuất hiện bên phải
4. **Tap send** → Gửi tin nhắn, send button biến mất
5. **Blur input** → Quay về toolbar icons bên trái

### Toolbar Expansion:
1. **Tap nút >** → Toolbar mở rộng, input thu nhỏ
2. **Chọn action** → Thực hiện action (camera/gallery/file)
3. **Tap nút <** → Toolbar đóng lại, input mở rộng

## 🚀 Technical Benefits

### Performance:
- **Ít re-render**: Logic state đơn giản hơn
- **Smooth transitions**: Không có animation conflicts
- **Memory efficient**: Ít component mounting/unmounting

### Maintainability:
- **Clear logic**: Dễ hiểu và debug
- **Modular design**: Dễ thêm/bớt features
- **Consistent behavior**: Predictable UX

### Accessibility:
- **Clear visual cues**: User biết được state hiện tại
- **Logical flow**: Theo natural user behavior
- **Touch targets**: Đủ lớn và rõ ràng

## 🔧 Implementation Details

### Key Components:
- **leftToolbarContainer**: Container cho toolbar bên trái
- **inputContainerCompact**: Style thu nhỏ input
- **Smart conditional rendering**: Logic hiển thị theo state

### State Management:
- **showToolbar**: Control toolbar expansion
- **isInputFocused**: Track input focus state
- **inputText**: Control send button visibility

Kết quả là một toolbar thông minh và send button hoạt động đúng theo yêu cầu! 🚀
