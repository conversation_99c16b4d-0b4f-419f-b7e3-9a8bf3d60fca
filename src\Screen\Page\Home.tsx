/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Animated,
  Dimensions,
  Platform,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {App<PERSON>utton, FBottomSheet} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import HomeHeader from '../Layout/headers/HomeHeader';
import {ScrollView} from 'react-native-gesture-handler';
import {DataController} from '../../base/baseController';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
import CategoryGrid from '../../modules/category/CategoryGrid';
import ProductBestSeller from '../../modules/Product/productBestSeller';
import ByNewTrending from '../../modules/news/listview/byTrending';
import HotProductsSection from '../../modules/Product/HotProductsSection';
import ByNewEvents from '../../modules/news/listview/byEvents';
import {useDispatch} from 'react-redux';
import {ProductActions} from '../../redux/reducers/ShoptReducer';
import CurrentPoint from '../../components/currentPoint';
import HeaderLogo from '../Layout/headers/HeaderLogo';

const Home = () => {
  const navigation = useNavigation<any>();
  const bottomSheetRef = useRef<any>(null);
  const {t} = useTranslation();
  // const { changeLanguage } = useLanguage();
  const bannerDA = new DataController('Banner');
  const [banners, setBanners] = useState<Array<any>>([]);
  const [isLoading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(0)).current; // Initial opacity value

  // Function to start the fade-in animation
  const fadeIn = () => {
    // Reset opacity to 0
    fadeAnim.setValue(0);

    // Start the fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000, // 1 second fade-in
      useNativeDriver: true,
    }).start();
  };

  const getBanner = async () => {
    setLoading(true);
    try {
      const result = await bannerDA.getAll();
      if (result) {
        setBanners(result.data);
        // Start fade animation after data is loaded
        fadeIn();
      }
    } catch (error) {
      console.error('Failed to fetch banners:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // State to trigger refreshes in child components
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefresh = () => {
    setRefreshing(true);

    // Trigger refresh in child components by updating the refreshTrigger
    // This will cause the useEffect to run and call getBanner()
    setRefreshTrigger(prev => prev + 1);
  };

  // Effect to get banner data on initial load and when refreshTrigger changes
  useEffect(() => {
    getBanner();
  }, [refreshTrigger]);

  // Start fade animation when component mounts
  useEffect(() => {
    fadeIn();
  }, []);

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <HeaderLogo />
      {/* <HomeHeader
        onSearchPress={() => {
          navigation.navigate(RootScreen.SearchIndex);
        }}
        notificationCount={0}
      /> */}
      {/* Main content */}
      <ScrollView
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }>
        {/* banner */}
        <TouchableOpacity
          onPress={() => {
            navigation.navigate(RootScreen.GiftExchange);
          }}
          style={{
            marginHorizontal: 16,
            marginTop: 24,
            borderRadius: 8,
            height: 176,
            overflow: 'hidden',
          }}>
          {/* Learn now button removed as it's now part of each slide */}
          <View
            style={{
              position: 'absolute',
              bottom: 16,
              left: 16,
              zIndex: 99,
            }}>
            <AppButton
              title={'Xen thêm'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={{
                ...TypoSkin.buttonText3,
                color: ColorThemes.light.neutral_absolute_background_color,
              }}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {}}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          </View>
          <SwiperFlatList
            autoplay
            autoplayDelay={7}
            autoplayLoop
            showPagination={false}
            pagingEnabled
            style={{width: Dimensions.get('window').width, height: '100%'}}
            data={banners}
            onChangeIndex={({index}) => {
              // Update active index and trigger fade animation
              setActiveIndex(index);
              fadeIn();
            }}
            renderItem={({item}) => (
              <View
                key={item.Img}
                style={{
                  width: Dimensions.get('window').width,
                  height: '100%',
                }}>
                <View
                  style={{
                    position: 'absolute',
                    bottom: 60,
                    left: 16,
                    zIndex: 99,
                  }}>
                  <Animated.Text
                    style={{
                      ...TypoSkin.heading6,
                      color:
                        ColorThemes.light.neutral_absolute_background_color,
                      opacity: fadeAnim, // Bind opacity to animated value
                    }}>
                    {item.Name}
                  </Animated.Text>
                </View>
                <FastImage
                  key={item.Img}
                  onError={() => {}}
                  source={{uri: ConfigAPI.urlImg + item.Img}}
                  style={{
                    width: '100%',
                    height: '100%',
                    backgroundColor: '#f2f2f2',
                  }}
                />
              </View>
            )}
          />
        </TouchableOpacity>
        {/* Danh mục */}
        <View style={styles.categorySection}>
          <CategoryGrid
            // categories={homeCategories}
            numColumns={3}
            onCategoryPress={category => {
              // Xử lý khi nhấn vào danh mục
              navigate(RootScreen.ProductListByCategory, {
                categoryId: category.Id,
                categoryName: category.Name,
              });
            }}
          />
        </View>

        {/* Sản phẩm HOT - Sử dụng component mới với dữ liệu thực từ API */}
        <HotProductsSection
          title="Sản phẩm HOT"
          pageSize={10}
          onSeeAll={() => navigation.navigate(RootScreen.AllHotProductsPage)}
          onRefresh={() => handleRefresh()}
        />

        {/* Sản phẩm nổi bật */}
        <ProductBestSeller
          horizontal={true}
          // titleList={'Sản phẩm nôi bật'}
          id="40caf5c67af14eb8b0c6de945d1d6f93"
          isSeeMore={true}
          onRefresh={() => {}}
          onPressSeeMore={() => {
            navigation.navigate(RootScreen.HotProductsDemo);
          }}
          key={`japan-advance-${refreshTrigger}`} // Force re-render on refresh
        />
        {/* hot event */}
        <ByNewEvents
          id="40caf5c67af14eb8b0c6de945d1d6f92"
          horizontal
          isSeeMore={true}
          onRefresh={() => {}}
          onPressSeeMore={() => {}}
          titleList={'Sự kiện'}
          key={`ByNewEvents-${refreshTrigger}`} // Force re-render on refresh
        />

        {/* hot event */}
        <ByNewTrending
          id="40caf5c67af14eb8b0c6de945d1d6f22"
          horizontal={false}
          isSeeMore={true}
          onRefresh={() => {}}
          onPressSeeMore={() => {}}
          titleList={'Tin tức'}
          key={`trending-${refreshTrigger}`} // Force re-render on refresh
        />
        <View style={{height: 100}} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
  },
  scrollContent: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
    marginTop: Platform.OS === 'android' ? 10 : -30, // Overlap with the header's wavy bottom
  },
  categorySection: {
    backgroundColor: 'white',
    borderRadius: 8,
  },
  titleText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  textContainer: {
    paddingRight: 10,
  },
  mainText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
});

export default Home;
