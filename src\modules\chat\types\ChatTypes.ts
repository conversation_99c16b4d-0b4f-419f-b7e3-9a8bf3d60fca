export interface ChatMessage {
  Id: string;
  Content: string;
  DateCreated: number;
  user?: ChatUser;
  FileUrl?: string;
  Type?: string;
  sent?: boolean;
  received?: boolean;
  ChatRoomId: string;
  CustomerId: string;
}

export interface ChatUser {
  Id: string | number;
  Name: string;
  Avatar?: string;
}

export interface QuickReplies {
  type: 'radio' | 'checkbox';
  values: QuickReply[];
  keepIt?: boolean;
}

export interface QuickReply {
  title: string;
  value: string;
  messageId?: any;
}

export interface ChatRoom {
  Id: string;
  id?: string; // For compatibility
  Name: string;
  name?: string; // For compatibility
  Avatar: string;
  CustomerId: string; // Comma-separated user IDs
  IsGroup: boolean;
  LastMessage: ChatMessage | null;
  DateCreated: number;
  UpdatedAt: number;
  Members: string; // Comma-separated user IDs
}

export interface ChatState {
  rooms: ChatRoom[];
  currentRoom: ChatRoom | null;
  messages: { [roomId: string]: ChatMessage[] };
  loading: boolean;
  error: string | null;
  isConnected: boolean;
}

export interface CreateGroupRequest {
  name: string;
  participants: string[];
  avatar?: string;
}
