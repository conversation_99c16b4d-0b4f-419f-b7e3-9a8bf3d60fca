// import auth from '@react-native-firebase/auth';
// import {randomGID} from '../../utils/Utils';
// import {Platform} from 'react-native';
// import {DataController} from '../../base/baseController';
// import {ComponentStatus, showSnackbar} from 'wini-mobile-components';

// export function initPhoneOtp() {
//   const subscriber = auth().onAuthStateChanged(async user => {
//     if (user) {
//       console.log('Người dùng đã đăng nhập:', user);
//     } else {
//       console.log('Không có người dùng nào đăng nhập.');
//     }
//   });
//   return subscriber; // unsubscribe on unmount
// }

// export async function signOutPhoneFb() {
//   if (Platform.OS === 'android') {
//     let user = auth().currentUser;
//     if (user)
//       user
//         .delete()
//         .then(() => console.log('User deleted'))
//         .catch(error => console.log(error));
//   }
//   auth().signOut();
//   // Đăng xuất
// }

// const logController = new DataController('LogOtp');
// let lastRequestTime = 0;
// let PhoneRequest = '';
// // Handle the button press

// export const signInWithPhoneFB = (phoneNumber: string): Promise<any> => {
//   if (phoneNumber.startsWith('0')) {
//     phoneNumber = phoneNumber.replace('0', '+84');
//   }
//   PhoneRequest = phoneNumber;
//   const now = Date.now();
//   let obj = {
//     Id: randomGID(),
//     Name: 'Gửi mã OTP',
//     DateCreated: now,
//     Sort: 1,
//     Mobile: phoneNumber,
//   };

//   return new Promise((resolve, reject) => {
//     if (now - lastRequestTime < 60000 && !__DEV__) {
//       // Chỉ cho phép gửi lại sau 60 giây
//       showSnackbar({
//         message: 'Vui lòng chờ 1 phút trước khi yêu cầu lại mã xác thực.',
//         status: ComponentStatus.ERROR,
//       });
//       reject(new Error('Error occurred during phone number verification'));
//       return null;
//     }

//     lastRequestTime = now;
//     auth()
//       .verifyPhoneNumber(phoneNumber, 60, false)
//       .on('state_changed', async phoneAuthSnapshot => {
//         switch (phoneAuthSnapshot.state) {
//           case auth.PhoneAuthState.CODE_SENT:
//             console.log('sent sms code', phoneAuthSnapshot);
//             resolve(phoneAuthSnapshot); // Resolve with the response
//             break;

//           case auth.PhoneAuthState.ERROR:
//             console.error('error', phoneAuthSnapshot);
//             if (
//               phoneAuthSnapshot.error?.message.includes(
//                 'auth/too-many-requests',
//               )
//             ) {
//               showSnackbar({
//                 message: `Bạn đã gửi quá nhiều lần. Vui lòng thử lại sau vài phút nữa.`,
//                 status: ComponentStatus.ERROR,
//               });
//             } else {
//               showSnackbar({
//                 message: 'Có lỗi xảy ra, vui lòng thử lại sau.',
//                 status: ComponentStatus.ERROR,
//               });
//             }

//             if (!__DEV__) {
//               logController.add([
//                 {
//                   ...obj,
//                   Message: `Error: ${phoneAuthSnapshot}`,
//                 },
//               ]);
//             }
//             reject(
//               new Error('Error occurred during phone number verification'),
//             );
//             break;

//           case auth.PhoneAuthState.AUTO_VERIFY_TIMEOUT:
//             console.error('auto verify timeout error', phoneAuthSnapshot);
//             reject(new Error('Auto verify timeout'));
//             break;

//           case auth.PhoneAuthState.AUTO_VERIFIED:
//             console.log('auto verified', phoneAuthSnapshot);

//             if (phoneAuthSnapshot.code === null) {
//               reject(
//                 new Error('Error occurred during phone number verification'),
//               );
//               return;
//             }
//             resolve(phoneAuthSnapshot); // Resolve with the response
//             break;
//         }
//       });
//   });
// };

// // Confirm the otp code
// export async function confirmCode(confirm: any, code: string) {
//   const now = Date.now();

//   let obj = {
//     Id: randomGID(),
//     Name: 'Xác nhận mã OTP',
//     DateCreated: now,
//     Sort: 1,
//     Mobile: PhoneRequest,
//   };
//   try {
//     if (confirm) {
//       // Sử dụng credential để xác thực
//       const credential = auth.PhoneAuthProvider.credential(
//         confirm.verificationId,
//         code,
//       );
//       // Đăng nhập bằng credential
//       var rsCredential = await auth().signInWithCredential(credential);
//       if (rsCredential.user) {
//         
//         console.log(
//           '=======doneotp=============================',
//           rsCredential.user,
//         );
//         return true;
//       } else {
//         return false;
//       }
//     }
//     // else {
//     // var rs = await confirm.confirm(code);
//     // console.log('=======doneotp=============================', rs);
//     // await signOutPhoneFb();
//     // return true;
//     // }
//   } catch (error) {
//     console.log('Invalid code.', error);
//     if (!__DEV__) {
//       logController.add([
//         {
//           ...obj,
//           Message: `Invalid code: ${error}`,
//         },
//       ]);
//     }
//     return error;
//   }
// }
