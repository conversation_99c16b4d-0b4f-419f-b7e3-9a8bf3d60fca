# Cải tiến Input Toolbar và Message Layout

## 🎯 Mục tiêu
Sửa lại input toolbar và message layout theo yêu cầu:
1. Toolbar ẩn/hiện khi focus input với mũi tên toggle
2. Icon emoji bên trong ô input
3. Icon gửi bên phải ô input
4. Nội dung tin nhắn nằm gọn trong khung hiển thị

## ✅ Những thay đổi đã thực hiện

### 1. Toolbar Ẩn/Hiện với Toggle Button

#### State Management:
```typescript
const [showToolbar, setShowToolbar] = useState(true);
const [isInputFocused, setIsInputFocused] = useState(false);
```

#### Toggle Logic:
```typescript
// Toggle toolbar button
<TouchableOpacity 
  style={styles.toggleButton} 
  onPress={() => setShowToolbar(!showToolbar)}
>
  <Text style={styles.toggleIcon}>{showToolbar ? '→' : '+'}</Text>
</TouchableOpacity>
```

#### Auto Hide/Show:
- **Khi focus input**: Toolbar tự động ẩn
- **Khi blur và input trống**: Toolbar tự động hiện
- **Khi đang gõ**: Toolbar ẩn
- **Manual toggle**: Người dùng có thể bấm nút toggle

### 2. Input Layout Mới

#### Structure:
```typescript
<View style={styles.inputToolbarContainer}>
  {/* Toolbar actions - ẩn/hiện khi focus */}
  {showToolbar && (
    <View style={styles.toolbarRow}>
      <TouchableOpacity onPress={handleLibraryPicker}>🖼️</TouchableOpacity>
      <TouchableOpacity onPress={handleCameraPicker}>📷</TouchableOpacity>
      <TouchableOpacity onPress={handleFilePicker}>📎</TouchableOpacity>
    </View>
  )}
  
  {/* Input row */}
  <View style={styles.inputRow}>
    {/* Toggle button */}
    <TouchableOpacity style={styles.toggleButton}>
      <Text>{showToolbar ? '→' : '+'}</Text>
    </TouchableOpacity>
    
    {/* Input container với emoji inside */}
    <View style={styles.inputContainer}>
      <InputToolbar {...props} />
      <TouchableOpacity style={styles.emojiButtonInside}>
        <Text>😊</Text>
      </TouchableOpacity>
    </View>
    
    {/* Send button bên phải */}
    {(inputText.trim() || isInputFocused) && (
      <TouchableOpacity style={styles.sendButton}>
        <Text>➤</Text>
      </TouchableOpacity>
    )}
  </View>
</View>
```

### 3. Emoji Button Inside Input

#### Design:
- **Position**: Bên trong ô input, phía bên phải
- **Style**: Icon 😊 không có background
- **Behavior**: Mở emoji picker khi bấm

#### Implementation:
```typescript
emojiButtonInside: {
  width: 32,
  height: 32,
  justifyContent: 'center',
  alignItems: 'center',
  marginRight: 4,
},
emojiIconInside: {
  fontSize: 20,
},
```

### 4. Send Button Bên Phải Input

#### Conditional Rendering:
```typescript
{(inputText.trim() || isInputFocused) && (
  <TouchableOpacity 
    style={styles.sendButton}
    onPress={() => {
      if (inputText.trim()) {
        const message = {
          _id: Date.now().toString(),
          text: inputText,
          createdAt: new Date(),
          user: { /* user info */ },
        };
        onSend([message]);
      }
    }}
  >
    <Text style={styles.sendIcon}>➤</Text>
  </TouchableOpacity>
)}
```

#### Features:
- **Hiển thị khi**: Có text hoặc input đang focus
- **Ẩn khi**: Input trống và không focus
- **Style**: Nút tròn màu primary với icon trắng
- **Position**: Bên phải input container

### 5. Input Container Styling

#### Enhanced Input Design:
```typescript
inputContainer: {
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: '#F5F5F5',
  borderRadius: 20,
  borderWidth: 1,
  borderColor: ColorThemes.light.neutral_border_color,
  paddingRight: 8,
},
```

#### Features:
- **Background**: Màu xám nhạt (#F5F5F5)
- **Border**: Border radius 20 cho look tròn
- **Layout**: Flexbox để chứa input và emoji button
- **Padding**: Padding phù hợp cho emoji button

### 6. Message Bubble Improvements

#### Constrained Width:
```typescript
bubbleRight: {
  backgroundColor: ColorThemes.light.primary_color,
  borderRadius: 18,
  marginVertical: 2,
  maxWidth: '80%',
  alignSelf: 'flex-end',
},
bubbleLeft: {
  backgroundColor: '#F0F0F0',
  borderRadius: 18,
  marginVertical: 2,
  maxWidth: '80%',
  alignSelf: 'flex-start',
},
```

#### Text Wrapping:
```typescript
textRight: {
  color: 'white',
  fontSize: 16,
  lineHeight: 20,
  flexWrap: 'wrap',
},
textLeft: {
  color: ColorThemes.light.neutral_text_color,
  fontSize: 16,
  lineHeight: 20,
  flexWrap: 'wrap',
},
```

#### Container Margins:
```typescript
containerStyle={{
  right: { marginRight: 8, marginLeft: 50 },
  left: { marginLeft: 8, marginRight: 50 },
}}
```

### 7. Focus/Blur Handling

#### TextInput Props:
```typescript
textInputProps={{
  onFocus: () => {
    setIsInputFocused(true);
    setShowToolbar(false);
  },
  onBlur: () => {
    setIsInputFocused(false);
    if (!inputText.trim()) {
      setShowToolbar(true);
    }
  },
}}
```

#### Input Text Change:
```typescript
const handleInputTextChange = (text: string) => {
  setInputText(text);
  
  if (text.length > 0) {
    SocketService.sendTyping(roomId);
    setShowToolbar(false); // Ẩn toolbar khi đang gõ
  } else if (!isInputFocused) {
    setShowToolbar(true); // Hiện toolbar khi input trống và không focus
  }
};
```

## 🎨 Visual Improvements

### Before vs After:

#### Before:
- Toolbar luôn hiển thị
- Emoji button riêng biệt
- Send button trong GiftedChat
- Message có thể tràn ra ngoài

#### After:
- Toolbar ẩn/hiện thông minh
- Emoji button tích hợp trong input
- Send button bên phải input
- Message nằm gọn trong 80% width

### Key Features:
1. **Smart Toolbar**: Tự động ẩn/hiện dựa trên trạng thái input
2. **Integrated Design**: Emoji và send button tích hợp với input
3. **Responsive Layout**: Message bubbles responsive với maxWidth
4. **Better UX**: Trải nghiệm người dùng mượt mà hơn

## 📱 Responsive Design

### Layout Constraints:
- **Message bubbles**: maxWidth 80% để không tràn
- **Input container**: Flex layout để responsive
- **Margins**: Proper margins để tránh overlap

### Touch Targets:
- **Toggle button**: 32x32 pt
- **Emoji button**: 32x32 pt  
- **Send button**: 36x36 pt
- **Action buttons**: 36x36 pt

## 🚀 Result

Input toolbar bây giờ có:
- ✅ Toolbar ẩn/hiện với toggle button (→/+)
- ✅ Emoji button bên trong ô input
- ✅ Send button bên phải ô input
- ✅ Message bubbles nằm gọn trong khung (80% width)
- ✅ Smart focus/blur handling
- ✅ Responsive design cho mọi screen size
- ✅ Trải nghiệm người dùng tốt hơn
