# Custom Chat UI - <PERSON>hay thế GiftedChat

## 🎯 Mục tiêu
Bỏ hoàn toàn GiftedChat và tự custom UI hiển thị messages để có control tốt hơn và performance cao hơn.

## ✅ Những thay đổi đã thực hiện

### 1. Loại bỏ GiftedChat Dependencies

#### Removed Imports:
```typescript
// TRƯỚC
import { GiftedChat, InputToolbar, Send, Bubble } from 'react-native-gifted-chat';

// SAU
import { 
  FlatList, 
  TextInput, 
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
```

### 2. Custom Message Rendering

#### Message Item Component:
```typescript
const renderMessageItem = ({ item: message }: { item: ChatMessage }) => {
  const isMyMessage = message.CustomerId === (customer?.Id || customer?.id);
  const messageTime = new Date(message.DateCreated).toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <View style={[
      styles.messageContainer,
      isMyMessage ? styles.myMessageContainer : styles.otherMessageContainer
    ]}>
      {/* Avatar for other user messages */}
      {!isMyMessage && (
        <View style={styles.avatarContainer}>
          {/* Avatar rendering logic */}
        </View>
      )}

      {/* Message bubble */}
      <View style={[
        styles.messageBubble,
        isMyMessage ? styles.myMessageBubble : styles.otherMessageBubble
      ]}>
        {/* Content based on message type */}
        {message.Type === '2' && message.FileUrl ? (
          // Image message
        ) : message.Type === '3' && message.FileUrl ? (
          // File message
        ) : (
          // Text/Emoji message
        )}
        
        {/* Message time */}
        <Text style={[
          styles.messageTime,
          isMyMessage ? styles.myMessageTime : styles.otherMessageTime
        ]}>
          {messageTime}
        </Text>
      </View>

      {/* Spacer for my messages */}
      {isMyMessage && <View style={styles.messageSpacer} />}
    </View>
  );
};
```

#### Message Type Handling:
- **Type '1'**: Text messages với styling khác nhau cho sent/received
- **Type '2'**: Image messages với FastImage và touch handler
- **Type '3'**: File messages với icon và filename
- **Type '4'**: Emoji messages (treated as text)

### 3. Custom Input Toolbar

#### TextInput thay vì InputToolbar:
```typescript
const renderInputToolbar = () => (
  <View style={styles.inputToolbarContainer}>
    {/* Toolbar actions */}
    {showToolbar && (
      <View style={styles.toolbarRow}>
        {/* Action buttons */}
      </View>
    )}
    
    {/* Input row */}
    <View style={styles.inputRow}>
      <TouchableOpacity style={styles.toggleButton}>
        <Text>{showToolbar ? '→' : '+'}</Text>
      </TouchableOpacity>
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={handleInputTextChange}
          placeholder="Bạn muốn nói gì?"
          multiline
          onFocus={() => {
            setIsInputFocused(true);
            setShowToolbar(false);
          }}
          onBlur={() => {
            setIsInputFocused(false);
            if (!inputText.trim()) {
              setShowToolbar(true);
            }
          }}
        />
        
        <TouchableOpacity style={styles.emojiButtonInside}>
          <Text>😊</Text>
        </TouchableOpacity>
      </View>
      
      {(inputText.trim() || isInputFocused) && (
        <TouchableOpacity style={styles.sendButton}>
          <Text>➤</Text>
        </TouchableOpacity>
      )}
    </View>
  </View>
);
```

### 4. FlatList thay vì GiftedChat

#### Main Chat Container:
```typescript
<KeyboardAvoidingView 
  style={styles.chatContainer}
  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
  keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
>
  <FlatList
    data={displayMessages}
    renderItem={renderMessageItem}
    keyExtractor={(item) => item.Id}
    style={styles.messagesList}
    contentContainerStyle={styles.messagesContent}
    showsVerticalScrollIndicator={false}
    onEndReached={() => {
      if (shouldShowLoadEarlier && !chatState.loading) {
        onLoadEarlier();
      }
    }}
    onEndReachedThreshold={0.1}
    ListEmptyComponent={renderEmptyState}
    ListHeaderComponent={loadingComponent}
    ListFooterComponent={typingComponent}
  />
  
  {renderInputToolbar()}
  
  <EmojiPicker />
</KeyboardAvoidingView>
```

#### FlatList Features:
- **Pagination**: onEndReached cho load more messages
- **Empty state**: ListEmptyComponent
- **Loading**: ListHeaderComponent
- **Typing indicator**: ListFooterComponent
- **Keyboard handling**: KeyboardAvoidingView

### 5. Message Sending Logic

#### handleSendMessage Function:
```typescript
const handleSendMessage = useCallback(async (text: string, type: string = '1', fileUrl?: string) => {
  if (!text.trim() && !fileUrl) return;

  try {
    setInputText('');

    const chatMessage: ChatMessage = {
      Id: generateUniqueId(),
      Content: text,
      DateCreated: new Date().getTime(),
      Type: type,
      FileUrl: fileUrl,
      CustomerId: customer?.Id || customer?.id || '',
      ChatRoomId: roomId,
      user: {
        Id: customer?.Id || customer?.id || '',
        Name: customer?.Name || customer?.name || '',
        Avatar: customer?.Avatar || customer?.avatar,
      },
      sent: true,
      received: false,
    };

    dispatch(addMessage({ roomId, message: chatMessage }));
    SocketService.sendMessage(roomId, chatMessage);
    await ChatAPI.sendMessage(chatMessage);

  } catch (error) {
    console.error('Error sending message:', error);
    showSnackbar({
      status: ComponentStatus.ERROR,
      message: 'Không thể gửi tin nhắn',
    });
  }
}, [roomId, customer, dispatch]);
```

#### Usage:
- **Text**: `handleSendMessage(text, '1')`
- **Image**: `handleSendMessage('', '2', imageUrl)`
- **File**: `handleSendMessage(filename, '3', fileUrl)`
- **Emoji**: `handleSendMessage(emoji, '4')`

### 6. Styling System

#### Message Styles:
```typescript
messageContainer: {
  flexDirection: 'row',
  marginVertical: 4,
  paddingHorizontal: 8,
},
myMessageContainer: {
  justifyContent: 'flex-end',
},
otherMessageContainer: {
  justifyContent: 'flex-start',
},
messageBubble: {
  maxWidth: '80%',
  paddingHorizontal: 12,
  paddingVertical: 8,
  borderRadius: 18,
  marginHorizontal: 8,
},
myMessageBubble: {
  backgroundColor: ColorThemes.light.primary_color,
  alignSelf: 'flex-end',
},
otherMessageBubble: {
  backgroundColor: '#F0F0F0',
  alignSelf: 'flex-start',
},
```

#### Input Styles:
```typescript
textInput: {
  flex: 1,
  fontSize: 16,
  paddingHorizontal: 12,
  paddingVertical: 8,
  maxHeight: 100,
  color: ColorThemes.light.neutral_text_color,
},
```

## 🚀 Benefits của Custom UI

### Performance:
- **Faster rendering**: Không có overhead của GiftedChat
- **Better memory**: Control được memory usage
- **Smooth scrolling**: FlatList optimization

### Customization:
- **Full control**: Có thể customize mọi aspect
- **Flexible layout**: Dễ thay đổi layout theo yêu cầu
- **Custom animations**: Có thể thêm animations

### Maintenance:
- **No dependencies**: Không phụ thuộc vào GiftedChat updates
- **Cleaner code**: Code rõ ràng và dễ hiểu
- **Better debugging**: Dễ debug hơn

## 📱 Features Implemented

### Message Display:
- ✅ Text messages với bubble styling
- ✅ Image messages với FastImage
- ✅ File messages với icon và filename
- ✅ Emoji messages
- ✅ Message timestamps
- ✅ Avatar cho received messages
- ✅ Different styling cho sent/received

### Input System:
- ✅ TextInput với multiline support
- ✅ Emoji button inside input
- ✅ Send button bên phải
- ✅ Toolbar ẩn/hiện
- ✅ Focus/blur handling

### Chat Features:
- ✅ Pagination (load more messages)
- ✅ Empty state
- ✅ Loading indicator
- ✅ Typing indicator
- ✅ Keyboard avoidance

### Message Types:
- ✅ Text (Type '1')
- ✅ Image (Type '2')
- ✅ File (Type '3')
- ✅ Emoji (Type '4')

## 🔧 Technical Implementation

### Data Flow:
1. **Messages từ Redux** → `displayMessages` array
2. **FlatList renders** → `renderMessageItem` cho mỗi message
3. **User input** → `handleSendMessage` → Redux + Socket + API
4. **Real-time updates** → Socket → Redux → FlatList re-render

### Key Components:
- **FlatList**: Main messages container
- **renderMessageItem**: Individual message renderer
- **renderInputToolbar**: Custom input system
- **handleSendMessage**: Message sending logic
- **KeyboardAvoidingView**: Keyboard handling

Kết quả là một chat UI hoàn toàn custom, performance cao và dễ maintain! 🚀
