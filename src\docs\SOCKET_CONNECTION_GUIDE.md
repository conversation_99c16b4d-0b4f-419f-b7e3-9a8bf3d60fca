# Socket Connection Guide - Hướng dẫn kết nối Socket

## 📋 Tổng quan

Hệ thống socket connection đã được tích hợp tự động vào flow đăng nhập/đăng xuất của ứng dụng Chainivo. Socket sẽ tự động kết nối khi user đăng nhập và ngắt kết nối khi user đăng xuất.

## 🔧 Cấu hình

### Socket URL
Socket URL được cấu hình trong `src/Config/ConfigAPI.tsx`:
```typescript
static Socketurl = 'https://apichanivo.wini.vn/';
```

### Các file liên quan
- `src/services/AuthSocketService.ts` - Service quản lý kết nối socket
- `src/modules/chat/services/SocketService.ts` - Service socket cơ bản
- `src/hooks/useSocketConnection.ts` - Hook để sử dụng trong components
- `src/redux/reducers/ChatReducer.tsx` - Redux state cho chat

## 🚀 Cách hoạt động

### 1. Khi app khởi động
- `SplashScreenWithAuthCheck` kiểm tra token
- Nếu có token → gọi `CustomerActions.getInfor()`
- Sau khi có thông tin user → tự động kết nối socket

### 2. Khi user đăng nhập
- Login thành công → lưu token
- Gọi `CustomerActions.getInfor()` để lấy thông tin user
- Tự động kết nối socket sau 1 giây

### 3. Khi user đăng xuất
- Gọi `CustomerActions.logout()`
- Tự động ngắt kết nối socket
- Clear token và navigate về login

## 📱 Sử dụng trong Components

### 1. Kiểm tra trạng thái kết nối
```typescript
import { useSocketStatus } from '../hooks/useSocketConnection';

const MyComponent = () => {
  const { isConnected, connectionStatus } = useSocketStatus();
  
  return (
    <View>
      {!isConnected && (
        <Text>Đang kết nối...</Text>
      )}
    </View>
  );
};
```

### 2. Quản lý kết nối thủ công
```typescript
import { useSocketConnection } from '../hooks/useSocketConnection';

const MyComponent = () => {
  const { 
    isConnected, 
    isConnecting, 
    connectionError, 
    connect, 
    disconnect, 
    retry 
  } = useSocketConnection();
  
  const handleRetry = async () => {
    const success = await retry();
    if (success) {
      console.log('Reconnected successfully');
    }
  };
  
  return (
    <View>
      {connectionError && (
        <TouchableOpacity onPress={handleRetry}>
          <Text>Retry Connection</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};
```

## 🔍 Debug và Monitoring

### Console Logs
Socket service sẽ log các sự kiện quan trọng:
```
🔌 Initializing socket connection...
🔌 Connecting socket for user: 12345
✅ Socket connection initialized successfully
🔌 Disconnecting socket...
✅ Socket disconnected successfully
```

### Kiểm tra trạng thái
```typescript
import AuthSocketService from '../services/AuthSocketService';

// Kiểm tra trạng thái kết nối
const isConnected = AuthSocketService.isConnected();
console.log('Socket connected:', isConnected);
```

## 🛠️ Troubleshooting

### 1. Socket không kết nối được
- Kiểm tra URL trong `ConfigAPI.Socketurl`
- Kiểm tra token có hợp lệ không
- Kiểm tra user ID có tồn tại không
- Xem console logs để debug

### 2. Socket bị ngắt kết nối
- Kiểm tra network connection
- Server có thể restart
- Token có thể hết hạn

### 3. Kết nối lại tự động
```typescript
// Socket service sẽ tự động retry khi mất kết nối
// Hoặc có thể gọi thủ công:
await AuthSocketService.retryConnection();
```

## 📋 Checklist Implementation

### ✅ Đã hoàn thành:
- [x] Tạo AuthSocketService để quản lý kết nối
- [x] Tích hợp vào SplashScreenWithAuthCheck
- [x] Tích hợp vào LoginScreen (username/password)
- [x] Tích hợp vào LoginScreen (social login)
- [x] Tích hợp vào logout action
- [x] Tạo hooks useSocketConnection và useSocketStatus
- [x] Hiển thị trạng thái kết nối trong ChatMainScreen
- [x] Sửa URL socket sử dụng ConfigAPI.Socketurl
- [x] Thêm error handling và retry logic

### 🔄 Auto-connect scenarios:
1. **App startup với token** → ✅ Auto-connect
2. **Login với username/password** → ✅ Auto-connect
3. **Login với social (Google)** → ✅ Auto-connect
4. **Login với biometric** → ✅ Auto-connect
5. **Logout** → ✅ Auto-disconnect

## 🎯 Lưu ý quan trọng

1. **Delay 1 giây**: Có delay 1 giây sau khi login để đảm bảo user info đã được load
2. **Token validation**: Socket chỉ kết nối khi có cả userId và token
3. **Redux state**: Trạng thái socket được lưu trong Redux store
4. **Error handling**: Có xử lý lỗi và retry mechanism
5. **Memory management**: Socket được disconnect đúng cách khi logout

## 🔧 Customization

### Thay đổi delay time
```typescript
// Trong login functions, thay đổi timeout:
setTimeout(async () => {
  await AuthSocketService.initializeSocketConnection();
}, 2000); // Thay đổi từ 1000ms thành 2000ms
```

### Thêm custom events
```typescript
// Trong SocketService.ts
onCustomEvent(callback: (data: any) => void) {
  if (this.socket) {
    this.socket.on('custom_event', callback);
  }
}
```

### Thêm authentication headers
```typescript
// Trong SocketService.ts connect method
this.socket = io(ConfigAPI.Socketurl, {
  auth: {
    token,
    userId,
    // Thêm custom headers
    customHeader: 'value',
  },
  // ...
});
```
