import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { ColorThemes } from '../assets/skin/colors';
import { useSocketConnection, useSocketStatus } from '../hooks/useSocketConnection';
import AuthSocketService from '../services/AuthSocketService';
import SocketService from '../modules/chat/services/SocketService';
import { useSelectorCustomerState } from '../redux/hook/customerHook';
import { getDataToAsyncStorage } from '../utils/AsyncStorage';
import { StorageContanst } from '../Config/Contanst';

/**
 * Component để test socket connection
 * Sử dụng để debug và kiểm tra các chức năng socket
 */
const SocketConnectionTest: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [token, setToken] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);

  const customer = useSelectorCustomerState().data;
  const { isConnected, connectionStatus } = useSocketStatus();
  const { 
    isConnecting, 
    connectionError, 
    connect, 
    disconnect, 
    retry 
  } = useSocketConnection();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  useEffect(() => {
    loadUserInfo();
  }, [customer]);

  const loadUserInfo = async () => {
    try {
      const accessToken = await getDataToAsyncStorage(StorageContanst.accessToken);
      setToken(accessToken);
      setUserId(customer?.id || customer?.Id || null);
      
      addLog(`User ID: ${customer?.id || customer?.Id || 'Not found'}`);
      addLog(`Token: ${accessToken ? 'Available' : 'Not found'}`);
    } catch (error) {
      addLog(`Error loading user info: ${error}`);
    }
  };

  const testManualConnect = async () => {
    addLog('Testing manual connect...');
    try {
      const success = await connect();
      addLog(`Manual connect result: ${success ? 'Success' : 'Failed'}`);
    } catch (error) {
      addLog(`Manual connect error: ${error}`);
    }
  };

  const testManualDisconnect = () => {
    addLog('Testing manual disconnect...');
    try {
      disconnect();
      addLog('Manual disconnect completed');
    } catch (error) {
      addLog(`Manual disconnect error: ${error}`);
    }
  };

  const testRetryConnection = async () => {
    addLog('Testing retry connection...');
    try {
      const success = await retry();
      addLog(`Retry result: ${success ? 'Success' : 'Failed'}`);
    } catch (error) {
      addLog(`Retry error: ${error}`);
    }
  };

  const testDirectSocketService = async () => {
    addLog('Testing direct SocketService...');
    try {
      if (!userId || !token) {
        addLog('Missing userId or token for direct test');
        return;
      }
      
      await SocketService.connect(userId, token);
      addLog('Direct SocketService connect successful');
    } catch (error) {
      addLog(`Direct SocketService error: ${error}`);
    }
  };

  const testAuthSocketService = async () => {
    addLog('Testing AuthSocketService...');
    try {
      const success = await AuthSocketService.initializeSocketConnection();
      addLog(`AuthSocketService result: ${success ? 'Success' : 'Failed'}`);
    } catch (error) {
      addLog(`AuthSocketService error: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const showConnectionInfo = () => {
    Alert.alert(
      'Connection Info',
      `Status: ${connectionStatus}\n` +
      `Connected: ${isConnected}\n` +
      `Connecting: ${isConnecting}\n` +
      `Error: ${connectionError || 'None'}\n` +
      `User ID: ${userId || 'None'}\n` +
      `Token: ${token ? 'Available' : 'None'}`
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Socket Connection Test</Text>
      
      {/* Status */}
      <View style={styles.statusContainer}>
        <View style={[styles.statusDot, { backgroundColor: isConnected ? '#4CAF50' : '#F44336' }]} />
        <Text style={styles.statusText}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </Text>
        {isConnecting && <Text style={styles.connectingText}> (Connecting...)</Text>}
      </View>

      {connectionError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {connectionError}</Text>
        </View>
      )}

      {/* Controls */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity style={styles.button} onPress={testManualConnect}>
          <Text style={styles.buttonText}>Manual Connect</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testManualDisconnect}>
          <Text style={styles.buttonText}>Manual Disconnect</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testRetryConnection}>
          <Text style={styles.buttonText}>Retry Connection</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testDirectSocketService}>
          <Text style={styles.buttonText}>Test Direct Socket</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testAuthSocketService}>
          <Text style={styles.buttonText}>Test Auth Socket</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={showConnectionInfo}>
          <Text style={styles.buttonText}>Show Info</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={loadUserInfo}>
          <Text style={styles.buttonText}>Reload User Info</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.clearButton} onPress={clearLogs}>
          <Text style={styles.buttonText}>Clear Logs</Text>
        </TouchableOpacity>
      </View>

      {/* Logs */}
      <View style={styles.logsContainer}>
        <Text style={styles.logsTitle}>Logs:</Text>
        <ScrollView style={styles.logsScroll}>
          {logs.map((log, index) => (
            <Text key={index} style={styles.logText}>{log}</Text>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: ColorThemes.light.neutral_text_color,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
  },
  connectingText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
  },
  controlsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_color,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 100,
  },
  clearButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 100,
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  logsContainer: {
    flex: 1,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_border_color,
    paddingTop: 16,
  },
  logsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_color,
  },
  logsScroll: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 8,
    borderRadius: 6,
  },
  logText: {
    fontSize: 12,
    color: '#333',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
});

export default SocketConnectionTest;
