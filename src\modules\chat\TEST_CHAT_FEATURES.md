# Test Chat Features - Hướng dẫn kiểm tra

## <PERSON><PERSON><PERSON> tính năng đã được cải thiện

### 1. Giao diện Chat mới với Tab Navigation ✅
- **Mô tả**: Tạo ChatMainScreen với 3 tab: <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> sử cuộc gọi
- **Cách test**: 
  1. Mở app → Bottom navigation → Tab "Chat"
  2. Ki<PERSON><PERSON> tra header giống trang Home
  3. <PERSON><PERSON>m tra 3 tab: <PERSON> (có badge 21), <PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON> sử cuộc gọi
  4. Kiểm tra search bar
  5. <PERSON><PERSON><PERSON> tra nút "Create group"

### 2. Tab Tin nhắn ✅
- **<PERSON><PERSON> tả**: Hi<PERSON><PERSON> thị danh sách cuộc trò chuyện
- **Cách test**:
  1. <PERSON><PERSON><PERSON> tab "Tin nhắn"
  2. Kiểm tra danh sách chat rooms
  3. Nhấn vào một cuộc trò chuyệ<PERSON> → Chuyển đến ChatRoomScreen

### 3. Tab <PERSON><PERSON> bạ ✅
- **<PERSON><PERSON> tả**: <PERSON><PERSON><PERSON> thị danh sách người dùng với trạng thái online/offline và Gold rank
- **Cách test**:
  1. Chọn tab "Danh bạ"
  2. Kiểm tra danh sách contacts
  3. Kiểm tra trạng thái online (chấm xanh)
  4. Kiểm tra Gold badge (👑 Gold)
  5. Kiểm tra thời gian "20 phút" cho offline users

### 4. Tab Lịch sử cuộc gọi ✅
- **Mô tả**: Hiển thị danh sách cuộc gọi với thời gian và icon
- **Cách test**:
  1. Chọn tab "Lịch sử cuộc gọi"
  2. Kiểm tra danh sách call history
  3. Kiểm tra thời gian "1:49 PM"
  4. Kiểm tra icon cuộc gọi (màu khác nhau cho incoming/outgoing/missed)
  5. Kiểm tra duration (60 giây, 1 phút 60 giây, 2 phút, etc.)

### 5. Sửa lỗi chọn ảnh từ thư viện ✅
- **Mô tả**: Cải thiện xử lý permissions và error handling
- **Cách test**:
  1. Vào ChatRoomScreen
  2. Nhấn nút camera (📷)
  3. Chọn "Thư viện"
  4. Kiểm tra có thể chọn ảnh từ gallery
  5. Kiểm tra console logs để debug

### 6. Sửa lỗi tin nhắn không hiển thị ✅
- **Mô tả**: Cải thiện onSend function và state management
- **Cách test**:
  1. Vào ChatRoomScreen
  2. Gửi tin nhắn text
  3. Kiểm tra tin nhắn hiển thị ngay lập tức
  4. Kiểm tra console logs
  5. Kiểm tra input text được clear sau khi gửi

### 7. Sửa lỗi EmojiPicker không hiển thị ✅
- **Mô tả**: Cải thiện event handling cho emoji button
- **Cách test**:
  1. Vào ChatRoomScreen
  2. Nhấn nút emoji (😊)
  3. Kiểm tra EmojiPicker modal hiển thị
  4. Chọn emoji và kiểm tra được thêm vào input
  5. Kiểm tra console logs

## Cách chạy test

### 1. Khởi động ứng dụng
```bash
cd d:\Chainivo
npm start
npm run android  # hoặc npm run ios
```

### 2. Navigation test
1. Mở app
2. Đi đến bottom navigation
3. Nhấn tab "Chat" (icon chat)
4. Kiểm tra ChatMainScreen hiển thị

### 3. Kiểm tra từng tab
- **Tab Tin nhắn**: Kiểm tra danh sách chat, nhấn vào item để vào chat room
- **Tab Danh bạ**: Kiểm tra danh sách contacts với status
- **Tab Lịch sử cuộc gọi**: Kiểm tra call history

### 4. Test chat functionality
1. Từ tab Tin nhắn, nhấn vào một cuộc trò chuyện
2. Test gửi tin nhắn text
3. Test chọn ảnh (camera/gallery)
4. Test emoji picker
5. Test create group (từ nút + Create group)

## Debug Console Logs

Kiểm tra các logs sau trong console:

### Image Picker
```
Opening image picker from library...
Image selected: {path: "...", width: 800, height: 600}
```

### Message Sending
```
Sending message: {_id: "...", text: "hello", createdAt: Date}
Adding message to store: {...}
Message sent successfully
```

### Emoji Picker
```
Emoji button pressed, current showEmojiPicker: false
Emoji selected: 😀
```

### Permissions
```
Permissions: {camera: true, photoLibrary: true, storage: true}
```

## Lưu ý

1. **Mock Data**: Hiện tại sử dụng mock data cho contacts và call history
2. **Real API**: Cần kết nối API thực cho production
3. **Permissions**: Đảm bảo app có quyền camera và storage
4. **Socket**: Kiểm tra kết nối socket cho real-time messaging

## Troubleshooting

### Nếu ảnh không chọn được:
1. Kiểm tra permissions trong device settings
2. Kiểm tra console logs cho errors
3. Thử restart app

### Nếu tin nhắn không hiển thị:
1. Kiểm tra Redux state
2. Kiểm tra console logs
3. Kiểm tra socket connection

### Nếu emoji picker không hiển thị:
1. Kiểm tra modal state
2. Kiểm tra console logs
3. Thử nhấn nút emoji nhiều lần
