# Cập nhật giao diện Chat Room theo style Messenger

## 🎯 Mục tiêu
Sửa lại giao diện ChatRoomScreen để giống với ứng dụng Messenger như trong ảnh mẫu.

## ✅ Những thay đổi đã thực hiện

### 1. Header Bar (Giống Messenger)

#### Thêm Header Component:
```typescript
const renderHeader = () => {
  return (
    <View style={styles.header}>
      <StatusBar barStyle="light-content" backgroundColor={primary_color} />
      <View style={styles.headerContent}>
        {/* Back button and avatar */}
        <View style={styles.headerLeft}>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backIcon}>←</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.avatarContainer}>
            <FastImage source={{ uri: roomAvatar }} style={styles.headerAvatar} />
            {isOnline && <View style={styles.onlineIndicator} />}
          </TouchableOpacity>

          <View style={styles.headerInfo}>
            <Text style={styles.headerName}>{roomName}</Text>
            <Text style={styles.headerStatus}>
              {isOnline ? 'Đang hoạt động' : 'Offline'}
            </Text>
          </View>
        </View>

        {/* Action buttons */}
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.headerButton}>
            <Text style={styles.headerButtonIcon}>🌐</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Text style={styles.headerButtonIcon}>🔍</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Text style={styles.headerButtonIcon}>📞</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};
```

#### Header Features:
- **Back button**: Nút quay lại với icon mũi tên
- **Avatar**: Ảnh đại diện người chat với online indicator
- **User info**: Tên và trạng thái online/offline
- **Action buttons**: Các nút chức năng (web, search, call)
- **Gradient background**: Màu primary với shadow

### 2. Input Toolbar (Giống Messenger)

#### Redesigned Input Area:
```typescript
const renderInputToolbar = (props: any) => (
  <View style={styles.inputToolbarContainer}>
    <View style={styles.inputRow}>
      {/* Left actions */}
      <TouchableOpacity style={styles.inputActionButton} onPress={handleLibraryPicker}>
        <Text style={styles.inputActionIcon}>🖼️</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.inputActionButton} onPress={handleCameraPicker}>
        <Text style={styles.inputActionIcon}>📷</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.inputActionButton} onPress={handleFilePicker}>
        <Text style={styles.inputActionIcon}>📎</Text>
      </TouchableOpacity>
      
      {/* Input field */}
      <InputToolbar {...props} containerStyle={styles.inputToolbar} />
      
      {/* Right actions */}
      <TouchableOpacity style={styles.inputActionButton} onPress={handleEmojiButtonPress}>
        <Text style={styles.inputActionIcon}>😊</Text>
      </TouchableOpacity>
    </View>
  </View>
);
```

#### Input Features:
- **Horizontal layout**: Tất cả buttons và input trên cùng 1 hàng
- **Rounded buttons**: Các nút action tròn với background màu primary
- **Rounded input**: Input field có border radius và border
- **Integrated actions**: Không còn floating actions, tất cả tích hợp trong toolbar

### 3. Send Button (Style Messenger)

#### Enhanced Send Button:
```typescript
const renderSend = (props: any) => (
  <Send {...props} containerStyle={styles.sendContainer}>
    <View style={styles.sendButton}>
      <Text style={styles.sendIcon}>➤</Text>
    </View>
  </Send>
);
```

#### Send Button Features:
- **Circular design**: Nút tròn với background màu primary
- **White icon**: Icon màu trắng trên background màu
- **Proper sizing**: Kích thước 36x36 phù hợp với input height

### 4. Message Bubbles (Enhanced Style)

#### Updated Bubble Styles:
```typescript
bubbleRight: {
  backgroundColor: ColorThemes.light.primary_color,
  borderRadius: 18,
  marginVertical: 2,
},
bubbleLeft: {
  backgroundColor: '#F0F0F0',
  borderRadius: 18,
  marginVertical: 2,
},
```

#### Bubble Features:
- **Rounded corners**: Border radius 18 cho look hiện đại
- **Better spacing**: Margin vertical cho spacing tốt hơn
- **Color contrast**: Màu xám nhạt cho tin nhắn nhận, primary cho tin nhắn gửi

### 5. Layout Structure

#### New Layout Hierarchy:
```typescript
<SafeAreaView style={styles.container}>
  {renderHeader()}
  <View style={styles.chatContainer}>
    <GiftedChat
      // ... props
      renderInputToolbar={renderInputToolbar}
      renderSend={renderSend}
      renderBubble={renderBubble}
      placeholder="Bạn muốn nói gì?"
    />
    <EmojiPicker />
  </View>
</SafeAreaView>
```

#### Layout Features:
- **SafeAreaView**: Đảm bảo không bị che bởi notch/status bar
- **Header separation**: Header riêng biệt với chat area
- **Proper nesting**: Chat container chứa GiftedChat và các components khác

### 6. Styling Improvements

#### Key Style Updates:
```typescript
// Header styles
header: {
  backgroundColor: ColorThemes.light.primary_color,
  paddingTop: Platform.OS === 'ios' ? 0 : 25,
  elevation: 4,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
},

// Input toolbar styles
inputToolbarContainer: {
  backgroundColor: 'white',
  borderTopWidth: 1,
  borderTopColor: ColorThemes.light.neutral_border_color,
  paddingHorizontal: 8,
  paddingVertical: 8,
},

inputRow: {
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: 8,
},

inputActionButton: {
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: ColorThemes.light.primary_color,
  justifyContent: 'center',
  alignItems: 'center',
  marginHorizontal: 4,
},
```

## 🎨 Visual Improvements

### Before vs After:
- **Before**: Floating action buttons, basic header, simple input
- **After**: Integrated toolbar, rich header with user info, Messenger-style layout

### Key Visual Elements:
1. **Header**: Gradient background, user avatar, online status, action buttons
2. **Input Area**: Horizontal layout, rounded buttons, integrated actions
3. **Messages**: Better bubble styling, improved spacing
4. **Overall**: More polished, professional Messenger-like appearance

### Color Scheme:
- **Primary**: Used for header background, action buttons, sent messages
- **Secondary**: Light gray for received messages
- **White**: Input background, header text
- **Accent**: Green for online indicator

## 📱 Responsive Design

### Platform Considerations:
- **iOS**: Proper SafeAreaView handling
- **Android**: Status bar padding adjustment
- **Universal**: Consistent spacing and sizing

### Accessibility:
- **Touch targets**: Minimum 44pt touch targets
- **Color contrast**: Proper contrast ratios
- **Text sizing**: Readable font sizes

## 🚀 Result

Giao diện ChatRoom bây giờ có:
- ✅ Header giống Messenger với avatar, tên, trạng thái
- ✅ Input toolbar tích hợp với các action buttons
- ✅ Send button tròn style hiện đại
- ✅ Message bubbles với styling tốt hơn
- ✅ Layout responsive và professional
- ✅ Trải nghiệm người dùng tương tự Messenger
