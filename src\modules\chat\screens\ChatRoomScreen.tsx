import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Platform,
} from 'react-native';
import { GiftedChat,  InputToolbar, Send, Bubble } from 'react-native-gifted-chat';
import { useDispatch } from 'react-redux';
import { useRoute } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
// import DocumentPicker from 'react-native-document-picker'; // Removed due to compatibility issues
import { ColorThemes } from '../../../assets/skin/colors';
import { useChatMessages, useSelectorChatState } from '../../../redux/hook/chatHook';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import {
  fetchMessages,
  addMessage,
  setCurrentRoom,
} from '../../../redux/reducers/ChatReducer';
import { ChatRoom, ChatMessage } from '../types/ChatTypes';
import SocketService from '../services/SocketService';
import ChatAPI from '../services/ChatAPI';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import EmojiPicker from '../components/EmojiPicker';
import { PermissionHelper } from '../utils/PermissionHelper';
import { MessageConverter } from '../utils/MessageConverter';
import FastImage from 'react-native-fast-image';

interface RouteParams {
  room: ChatRoom;
}

const ChatRoomScreen: React.FC = () => {
  const dispatch = useDispatch<any>();
  const route = useRoute<any>();
  const { room } = route.params as RouteParams;



  const customer = useSelectorCustomerState().data;
  const roomId = room.Id || room.id || '';
  const messages = useChatMessages(roomId);
  const chatState = useSelectorChatState();

  const [isTyping, setIsTyping] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [inputText, setInputText] = useState('');

  useEffect(() => {
    if (room && roomId) {
      dispatch(setCurrentRoom(room));
      dispatch(fetchMessages(roomId, 1));
      // dispatch(resetUnreadCount(roomId));

      // Join room for socket
      SocketService.joinRoom(roomId);

      // Setup socket listeners
      SocketService.onReceiveMessage((data) => {
        const { roomId: messageRoomId, fromUserId, message } = data;
        if (messageRoomId === roomId) {
          // Ensure message is in proper database format
          const chatMessage: ChatMessage = {
            Id: message.Id || Date.now().toString(),
            Content: message.Content || message.text || message,
            DateCreated: message.DateCreated || new Date().getTime(),
            Type: message.Type || '1', // Default to text
            FileUrl: message.FileUrl,
            CustomerId: fromUserId,
            ChatRoomId: messageRoomId,
            user: {
              Id: fromUserId,
              Name: 'User', // TODO: Get user name from contacts
            },
            received: true,
          };
          dispatch(addMessage({ roomId: messageRoomId, message: chatMessage }));
        }
      });

      // Setup typing listener
      SocketService.onUserTyping((data) => {
        const { roomId: typingRoomId, fromUserId } = data;
        if (typingRoomId === roomId && fromUserId !== (customer?.Id || customer?.id)) {
          setIsTyping(true);
          // Clear typing after 3 seconds
          setTimeout(() => setIsTyping(false), 3000);
        }
      });
    }

    return () => {
      if (roomId) {
        SocketService.leaveRoom(roomId);
      }
      dispatch(setCurrentRoom(null));
    };
  }, [roomId, dispatch, customer]);

  // Convert database messages to GiftedChat format
  const giftedChatMessages = MessageConverter.toGiftedChatArray(messages);

  // Generate random background color for avatar
  const getRandomAvatarColor = (name: string) => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const onSend = useCallback(async (newMessages: any[]) => {
    const message = newMessages[0];
    console.log('Sending message:', message);

    try {
      // Clear input text immediately
      setInputText('');

      // Convert GiftedChat message to database format
      const dbMessageData = MessageConverter.toDatabaseFormat(
        message,
        customer?.Id || customer?.id || '',
        roomId
      );

      // Create complete ChatMessage with user info
      const chatMessage: ChatMessage = {
          ...dbMessageData,
          user: {
            Id: customer?.Id || customer?.id || '',
            Name: customer?.Name || customer?.name || '',
            Avatar: customer?.Avatar || customer?.avatar,
          },
          sent: true,
          received: false,
      } as ChatMessage;

      console.log('Adding message to store:', chatMessage);
      dispatch(addMessage({ roomId, message: chatMessage }));

      // Send via socket
      SocketService.sendMessage(roomId, chatMessage);

      // Also send via API for persistence
      await ChatAPI.sendMessage(chatMessage);

      console.log('Message sent successfully');

    } catch (error) {
      console.error('Error sending message:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể gửi tin nhắn',
      });
    }
  }, [roomId, customer, dispatch]);

  const onLoadEarlier = useCallback(async () => {
    if (chatState.loading || !hasMoreMessages) return;

    try {
      const nextPage = page + 1;
      console.log('Loading earlier messages, page:', nextPage, 'current messages:', messages.length);

      const response = await ChatAPI.getMessages(roomId, nextPage);

      if (response.data.length === 0) {
        console.log('No more messages, disabling load earlier');
        setHasMoreMessages(false);
      } else {
        console.log('Loaded', response.data.length, 'earlier messages');
        setPage(nextPage);
        // Messages will be added via fetchMessages action
        dispatch(fetchMessages(roomId, nextPage));
      }
    } catch (error) {
      console.error('Error loading earlier messages:', error);
      setHasMoreMessages(false); // Disable on error
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải tin nhắn cũ',
      });
    }
  }, [roomId, page, hasMoreMessages, chatState.loading, dispatch, messages.length]);

  // Only show load earlier if we have enough messages and there might be more
  const shouldShowLoadEarlier = messages.length >= 10 && hasMoreMessages;

  const handleLibraryPicker = async () => {
    try {
      const permissions = await PermissionHelper.checkAllChatPermissions();
      console.log('Library permissions:', permissions);
      pickImageFromLibrary();
    } catch (error) {
      console.error('Error in handleLibraryPicker:', error);
      Alert.alert('Lỗi', 'Không thể mở thư viện ảnh');
    }
  };

  const handleCameraPicker = async () => {
    try {
      const permissions = await PermissionHelper.checkAllChatPermissions();
      console.log('Camera permissions:', permissions);
      pickImageFromCamera();
    } catch (error) {
      console.error('Error in handleCameraPicker:', error);
      Alert.alert('Lỗi', 'Không thể mở camera');
    }
  };

  const pickImageFromLibrary = () => {
    console.log('Opening image picker from library...');
    ImagePicker.openPicker({
      width: 800,
      height: 600,
      cropping: false,
      mediaType: 'photo',
      includeBase64: false,
      compressImageQuality: 0.8,
    }).then(async (image) => {
      console.log('Image selected:', image);
      await sendImageMessage(image);
    }).catch((error) => {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Image picker error:', error);
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể chọn ảnh từ thư viện',
        });
      }
    });
  };

  const pickImageFromCamera = () => {
    console.log('Opening camera...');
    ImagePicker.openCamera({
      width: 800,
      height: 600,
      cropping: false,
      mediaType: 'photo',
      includeBase64: false,
      compressImageQuality: 0.8,
    }).then(async (image) => {
      console.log('Image captured:', image);
      await sendImageMessage(image);
    }).catch((error) => {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Camera error:', error);
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể chụp ảnh',
        });
      }
    });
  };

  const sendImageMessage = async (image: any) => {
    try {
      // Upload image first
      const imageUrl = await ChatAPI.uploadChatFile(image, 'image');

      // Create message in GiftedChat format for onSend
      const giftedMessage = {
        _id: Date.now().toString(),
        text: '',
        createdAt: new Date(),
        user: {
          _id: customer?.Id || customer?.id || '',
          name: customer?.Name || customer?.name || '',
          avatar: customer?.Avatar || customer?.avatar,
        },
        image: imageUrl,
      };

      // Send through onSend which will convert to database format
      onSend([giftedMessage]);
    } catch (error) {
      console.error('Error sending image:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể gửi ảnh',
      });
    }
  };

  const handleFilePicker = async () => {
    // Tạm thời disable file picker do vấn đề tương thích
    showSnackbar({
      status: ComponentStatus.INFOR,
      message: 'Tính năng gửi file sẽ được cập nhật trong phiên bản tiếp theo',
    });

    // TODO: Implement file picker with alternative solution
    // Options:
    // 1. Use react-native-image-picker for images/videos
    // 2. Use custom file browser
    // 3. Wait for react-native-document-picker update
  };

  const renderInputToolbar = (props: any) => (
    <InputToolbar
      {...props}
      containerStyle={styles.inputToolbar}
      primaryStyle={styles.inputPrimary}
    />
  );

  const renderSend = (props: any) => (
    <Send {...props} containerStyle={styles.sendContainer}>
      <Text style={styles.sendIcon}>➤</Text>
    </Send>
  );

  const renderBubble = (props: any) => (
    <Bubble
      {...props}
      wrapperStyle={{
        right: styles.bubbleRight,
        left: styles.bubbleLeft,
      }}
      textStyle={{
        right: styles.textRight,
        left: styles.textLeft,
      }}
      renderMessageImage={(messageImageProps) => (
        <TouchableOpacity
          style={styles.imageContainer}
          onPress={() => {
            // TODO: Open image viewer/gallery
            console.log('Image pressed:', messageImageProps.currentMessage?.image);
          }}
          activeOpacity={0.8}
        >
          <FastImage
            source={{ uri: messageImageProps.currentMessage?.image }}
            style={styles.messageImage}
            resizeMode={FastImage.resizeMode.cover}
          />
        </TouchableOpacity>
      )}
      renderCustomView={(bubbleProps) => {
        const message = bubbleProps.currentMessage;
        // Render file attachment if exists
        if (message?.file) {
          return (
            <TouchableOpacity
              style={styles.fileContainer}
              onPress={() => {
                // TODO: Open file
                console.log('File pressed:', message.file);
              }}
            >
              <Text style={styles.fileIcon}>📎</Text>
              <Text style={styles.fileName}>{message.file.name || 'File'}</Text>
            </TouchableOpacity>
          );
        }
        return null;
      }}
    />
  );

  const handleEmojiSelect = (emoji: string) => {
    console.log('Emoji selected:', emoji);

    // If user wants to send emoji immediately (you can change this behavior)
    if (inputText.trim() === '') {
      // Send emoji as separate message
      const emojiMessage = {
        _id: Date.now().toString(),
        text: emoji,
        createdAt: new Date(),
        user: {
          _id: customer?.Id || customer?.id || '',
          name: customer?.Name || customer?.name || '',
          avatar: customer?.Avatar || customer?.avatar,
        },
      };
      onSend([emojiMessage]);
      setShowEmojiPicker(false);
    } else {
      // Add emoji to current input text
      setInputText(prev => prev + emoji);
    }
  };

  const handleEmojiButtonPress = () => {
    console.log('Emoji button pressed, current showEmojiPicker:', showEmojiPicker);
    setShowEmojiPicker(true);
  };

  const handleInputTextChange = (text: string) => {
    setInputText(text);

    // Send typing indicator
    if (text.length > 0) {
      SocketService.sendTyping(roomId);
    }
  };

  // Render empty state when no messages
  const renderEmptyState = () => {
    const roomName = room.Name || room.name || 'Người dùng';
    const roomAvatar = room.Avatar;
    const avatarColor = getRandomAvatarColor(roomName);

    return (
      <View style={styles.emptyStateContainer}>
        <View style={styles.emptyStateContent}>
          {roomAvatar ? (
            <FastImage source={{ uri: roomAvatar }} style={styles.emptyStateAvatar} />
          ) : (
            <View style={[styles.emptyStateAvatar, styles.emptyStateAvatarDefault, { backgroundColor: avatarColor }]}>
              <Text style={styles.emptyStateAvatarText}>
                {roomName.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          <Text style={styles.emptyStateText}>Hãy bắt đầu trò chuyện</Text>
        </View>
      </View>
    );
  };

  const renderActions = () => (
    <View style={styles.actionsContainer}>
      <TouchableOpacity style={styles.actionButton} onPress={handleLibraryPicker}>
        <Text style={styles.actionText}>🖼️</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.actionButton} onPress={handleCameraPicker}>
        <Text style={styles.actionText}>📷</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.actionButton} onPress={handleFilePicker}>
        <Text style={styles.actionText}>📎</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.actionButton}
        onPress={handleEmojiButtonPress}
      >
        <Text style={styles.actionText}>😊</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <GiftedChat
        messages={giftedChatMessages}
        onSend={onSend}
        user={{
          _id: customer?.id || '',
          name: customer?.name || '',
          avatar: customer?.avatar,
        }}
        renderInputToolbar={renderInputToolbar}
        renderSend={renderSend}
        renderBubble={renderBubble}
        loadEarlier={shouldShowLoadEarlier}
        onLoadEarlier={onLoadEarlier}
        isLoadingEarlier={chatState.loading}
        placeholder="Nhập tin nhắn..."
        alwaysShowSend
        inverted={true}
        scrollToBottomComponent={() => (
          <View style={styles.scrollToBottomStyle}>
            <Text style={styles.scrollToBottomText}>↓</Text>
          </View>
        )}
        text={inputText}
        onInputTextChanged={handleInputTextChange}
        renderFooter={() => isTyping ? (
          <View style={styles.typingContainer}>
            <Text style={styles.typingText}>Đang gõ...</Text>
          </View>
        ) : null}
        renderChatEmpty={renderEmptyState}
      />
      {renderActions()}

      <EmojiPicker
        visible={showEmojiPicker}
        onClose={() => setShowEmojiPicker(false)}
        onEmojiSelect={handleEmojiSelect}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  inputToolbar: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_border_color,
    paddingHorizontal: 8,
  },
  inputPrimary: {
    alignItems: 'center',
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 8,
  },
  sendText: {
    color: ColorThemes.light.primary_color,
    fontWeight: '600',
    fontSize: 16,
  },
  sendIcon: {
    color: ColorThemes.light.primary_color,
    fontSize: 20,
    fontWeight: 'bold',
  },
  bubbleRight: {
    backgroundColor: ColorThemes.light.primary_color,
  },
  bubbleLeft: {
    backgroundColor: 'white',
  },
  textRight: {
    color: 'white',
  },
  textLeft: {
    color: ColorThemes.light.neutral_text_color,
  },
  actionsContainer: {
    position: 'absolute',
    bottom: 60,
    left: 8,
    flexDirection: 'row',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  actionText: {
    fontSize: 18,
  },
  scrollToBottomStyle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  scrollToBottomText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
  },
  typingText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    fontStyle: 'italic',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    transform: [{ scaleY: -1 }, { scaleX: -1 }],
    marginVertical: 100, 
  },
  emptyStateContent: {
    alignItems: 'center',
    justifyContent: 'center',

  },
  emptyStateAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 24,
  },
  emptyStateAvatarDefault: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateAvatarText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: 'white',
  },
  emptyStateText: {
    fontSize: 18,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    fontWeight: '500',
  },
  imageContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 4,
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: ColorThemes.light.neutral_background_color,
    borderRadius: 8,
    marginVertical: 4,
    minWidth: 150,
  },
  fileIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  fileName: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_color,
    flex: 1,
  },
});

export default ChatRoomScreen;
