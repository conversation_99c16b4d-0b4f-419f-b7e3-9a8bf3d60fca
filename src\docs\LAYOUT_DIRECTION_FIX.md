# Layout Direction Fix - Sửa lỗi giao diện bị ngược

## 🚨 Vấn đề

Giao diện ChatRoomScreen bị hiển thị ngược (RTL - Right to Left) thay vì LTR (Left to Right) bình thường.

## 🔍 Nguyên nhân có thể

1. **I18nManager RTL**: App có thể đã enable RTL layout
2. **GiftedChat inverted**: GiftedChat có prop `inverted` mặc định
3. **Device settings**: Thiết bị có thể set RTL language
4. **CSS direction**: Styles có thể có direction: rtl

## ✅ Giải pháp đã áp dụng

### 1. Force LTR trong index.js
```javascript
import { I18nManager } from 'react-native';

// Force LTR layout
I18nManager.allowRTL(false);
I18nManager.forceRTL(false);
```

### 2. Cập nhật ChatRoomScreen
```typescript
// Force LTR layout trong component
useEffect(() => {
  I18nManager.allowRTL(false);
  I18nManager.forceRTL(false);
}, []);

// Container với direction LTR
<View style={[styles.container, { direction: 'ltr' }]}>

// GiftedChat với inverted=false
<GiftedChat
  inverted={false}
  // ... other props
/>
```

### 3. Styles với LTR direction
```typescript
container: {
  flex: 1,
  backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  direction: 'ltr',
  writingDirection: 'ltr',
}
```

## 🔧 Cách test

### 1. Restart app hoàn toàn
```bash
# Kill app completely
npx react-native run-android --reset-cache
# hoặc
npx react-native run-ios --reset-cache
```

### 2. Kiểm tra I18nManager
```typescript
import { I18nManager } from 'react-native';

console.log('RTL enabled:', I18nManager.isRTL);
console.log('Force RTL:', I18nManager.forceRTL);
```

### 3. Test layout direction
1. Mở ChatRoomScreen
2. Kiểm tra tin nhắn hiển thị đúng chiều (trái sang phải)
3. Kiểm tra input toolbar ở dưới cùng
4. Kiểm tra empty state centered

## 🚨 Lưu ý quan trọng

### Cần restart app
Thay đổi I18nManager **BẮT BUỘC** phải restart app hoàn toàn để có hiệu lực:
- Close app completely
- Clear cache nếu cần
- Rebuild và run lại

### Kiểm tra device settings
Một số thiết bị có thể force RTL:
- Android: Settings → Language → RTL languages
- iOS: Settings → General → Language & Region

### GiftedChat version
Kiểm tra version GiftedChat trong package.json:
```json
"react-native-gifted-chat": "^2.x.x"
```

## 🔍 Debug steps

### 1. Console logs
Thêm vào ChatRoomScreen:
```typescript
useEffect(() => {
  console.log('I18nManager.isRTL:', I18nManager.isRTL);
  console.log('I18nManager.doLeftAndRightSwapInRTL:', I18nManager.doLeftAndRightSwapInRTL);
}, []);
```

### 2. Test với component đơn giản
```typescript
const TestComponent = () => (
  <View style={{ flex: 1, flexDirection: 'row' }}>
    <View style={{ width: 50, height: 50, backgroundColor: 'red' }} />
    <View style={{ width: 50, height: 50, backgroundColor: 'blue' }} />
  </View>
);
```

### 3. Kiểm tra styles
```typescript
// Thêm border để debug layout
container: {
  flex: 1,
  borderWidth: 2,
  borderColor: 'red', // Debug border
  direction: 'ltr',
}
```

## 🛠️ Alternative solutions

### Nếu vẫn không work:

1. **Downgrade GiftedChat**:
   ```bash
   npm install react-native-gifted-chat@1.16.3
   ```

2. **Custom chat component**:
   Tạo chat component riêng thay vì dùng GiftedChat

3. **Force styles**:
   ```typescript
   messagesContainerStyle={{
     transform: [{ scaleX: -1 }]
   }}
   ```

4. **Check other libraries**:
   Kiểm tra các library khác có thể affect layout direction

## 📋 Checklist

### ✅ Đã thực hiện:
- [x] Thêm I18nManager.forceRTL(false) vào index.js
- [x] Thêm direction: 'ltr' vào container styles
- [x] Set inverted={false} cho GiftedChat
- [x] Thêm useEffect force LTR trong component

### 🔄 Cần làm:
- [ ] Restart app hoàn toàn
- [ ] Test trên device thật
- [ ] Kiểm tra console logs
- [ ] Verify layout direction

## 🎯 Expected result

Sau khi áp dụng fix và restart app:
- Tin nhắn hiển thị từ trái sang phải
- Input toolbar ở dưới cùng
- Empty state centered đúng
- Không có layout bị flip/mirror

Nếu vẫn không work, cần debug thêm với console logs và kiểm tra device settings!
