# Debug RTL Issue - <PERSON><PERSON><PERSON><PERSON> quyết vấn đề chữ bị ngược

## 🚨 Vấn đề hiện tại

1. **Chữ bị lộn ngược** trong tin nhắn
2. **"Hãy bắt đầu trò chuyện" bị ngược**
3. **Avatar empty state** bị quay ngược sát với input

## 🔍 Các cách đã thử

### ✅ 1. Force LTR trong nhiều nơi
- `index.js`: `I18nManager.forceRTL(false)`
- `App.tsx`: `I18nManager.forceRTL(false)`
- `ChatRoomScreen`: `useEffect` force LTR

### ✅ 2. GiftedChat props
- `inverted={false}` và `inverted={true}` (cả 2 đều test)
- `isKeyboardInternallyHandled={false}`
- `messagesContainerStyle`

### ✅ 3. CSS Direction
- `direction: 'ltr'` trong styles
- `writingDirection: 'ltr'`
- Transform `scaleX: -1` (đã remove)

## 🛠️ Debug Steps

### Step 1: Ki<PERSON>m tra I18nManager
Thêm vào ChatRoomScreen để debug:

```typescript
useEffect(() => {
  console.log('=== RTL DEBUG ===');
  console.log('I18nManager.isRTL:', I18nManager.isRTL);
  console.log('I18nManager.doLeftAndRightSwapInRTL:', I18nManager.doLeftAndRightSwapInRTL);
  console.log('I18nManager.allowRTL:', I18nManager.allowRTL);
}, []);
```

### Step 2: Test với component đơn giản
Tạo component test để kiểm tra layout direction:

```typescript
const TestRTL = () => (
  <View style={{ flexDirection: 'row', padding: 20 }}>
    <View style={{ width: 50, height: 50, backgroundColor: 'red' }} />
    <Text style={{ marginLeft: 10 }}>Text should be on the right</Text>
  </View>
);
```

### Step 3: Kiểm tra device settings
- Android: Settings → Language → RTL languages
- iOS: Settings → General → Language & Region

## 🔧 Solutions to try

### Solution 1: Restart app với clear cache
```bash
# Kill app completely
adb shell am force-stop com.yourapp.package

# Clear cache and restart
npx react-native run-android --reset-cache
```

### Solution 2: Check GiftedChat version
```bash
npm list react-native-gifted-chat
```

Nếu version > 2.0.0, có thể downgrade:
```bash
npm install react-native-gifted-chat@1.16.3
```

### Solution 3: Custom Text component
Tạo wrapper Text component force LTR:

```typescript
const LTRText: React.FC<TextProps> = ({ style, ...props }) => (
  <Text 
    {...props} 
    style={[
      { writingDirection: 'ltr', textAlign: 'left' }, 
      style
    ]} 
  />
);
```

### Solution 4: Force styles trong GiftedChat
```typescript
<GiftedChat
  renderBubble={(props) => (
    <Bubble
      {...props}
      textStyle={{
        right: { writingDirection: 'ltr', textAlign: 'left' },
        left: { writingDirection: 'ltr', textAlign: 'left' },
      }}
    />
  )}
/>
```

### Solution 5: Check Metro config
Kiểm tra `metro.config.js` có settings RTL không:

```javascript
module.exports = {
  transformer: {
    // ... other configs
  },
  resolver: {
    // ... other configs
  },
};
```

## 🎯 Next Actions

### Immediate (cần làm ngay):

1. **Restart app hoàn toàn** với clear cache
2. **Check console logs** cho I18nManager values
3. **Test trên device khác** (nếu có)

### If still not working:

1. **Downgrade GiftedChat** to stable version
2. **Create custom chat component** thay vì dùng GiftedChat
3. **Check other libraries** có thể conflict

## 📱 Test Commands

### Debug I18nManager
```typescript
// Thêm vào ChatRoomScreen
console.log('RTL Status:', {
  isRTL: I18nManager.isRTL,
  allowRTL: I18nManager.allowRTL,
  doLeftAndRightSwapInRTL: I18nManager.doLeftAndRightSwapInRTL,
});
```

### Force reload app
```bash
# Android
adb shell input keyevent 82  # Open dev menu
# Then select "Reload"

# Or restart completely
npx react-native run-android --reset-cache
```

### Check device language
```bash
# Android
adb shell getprop persist.sys.locale

# Should show language like "en-US", not RTL language
```

## 🚨 Critical Notes

### RTL Languages that cause issues:
- Arabic (ar)
- Hebrew (he) 
- Persian (fa)
- Urdu (ur)

### If device is set to RTL language:
App sẽ automatically enable RTL layout bất kể code settings.

### GiftedChat RTL behavior:
- `inverted={true}`: Messages từ bottom lên top (normal chat)
- `inverted={false}`: Messages từ top xuống bottom
- RTL + inverted có thể gây conflict

## 🔍 Expected Results

Sau khi fix:
- ✅ Tin nhắn hiển thị từ trái sang phải
- ✅ Text "Hãy bắt đầu trò chuyện" đọc được bình thường
- ✅ Avatar empty state ở giữa màn hình
- ✅ Input toolbar ở dưới cùng
- ✅ Không có text bị mirror/flip

## 📋 Debug Checklist

- [ ] Restart app với --reset-cache
- [ ] Check console logs cho I18nManager
- [ ] Test trên device khác
- [ ] Check device language settings
- [ ] Test với GiftedChat version khác
- [ ] Try custom Text component
- [ ] Check Metro config

Nếu tất cả đều không work, có thể cần tạo custom chat component thay vì dùng GiftedChat! 🤔
