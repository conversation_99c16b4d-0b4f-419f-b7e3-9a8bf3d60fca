# Test Empty State - ChatRoom Screen

## 🎯 Mục tiêu

Test giao diện empty state của ChatRoomScreen khi chưa có lịch sử chat theo thiết kế mới:
- Avatar của đối tượng muốn chat (120x120px)
- Text "Hãy bắt đầu trò chuyện" bên dưới avatar
- N<PERSON>u không có avatar → Hình tròn với chữ cái đầu và màu background random

## ✅ Đã triển khai

### 1. Empty State Component
- **Avatar hiển thị**: 120x120px, border radius 60px
- **Fallback avatar**: Hình tròn với chữ cái đầu tên (48px font size)
- **Random colors**: 10 màu khác nhau dựa trên tên
- **Text**: "Hãy bắt đầu trò chuyện" (18px, màu secondary)

### 2. Random Avatar Colors
```typescript
const colors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
];
```

### 3. Integration
- Sử dụng `renderChatEmpty` prop của GiftedChat
- Hiển thị khi `messages.length === 0`
- Responsive design với padding và centering

## 🧪 Test Cases

### Test Case 1: User có avatar
**Setup:**
1. Tạo chat với user có Avatar URL
2. Đảm bảo chưa có tin nhắn nào

**Expected:**
- Hiển thị FastImage với avatar URL
- Kích thước 120x120px
- Text "Hãy bắt đầu trò chuyện" bên dưới

**Steps:**
1. Vào ContactsScreen
2. Chọn user có avatar → Nhấn chat
3. Kiểm tra empty state hiển thị đúng

### Test Case 2: User không có avatar
**Setup:**
1. Tạo chat với user không có Avatar
2. Đảm bảo chưa có tin nhắn nào

**Expected:**
- Hiển thị hình tròn với background color random
- Chữ cái đầu của tên (uppercase, 48px, màu trắng)
- Text "Hãy bắt đầu trò chuyện" bên dưới

**Steps:**
1. Vào ContactsScreen
2. Chọn user không có avatar → Nhấn chat
3. Kiểm tra fallback avatar hiển thị đúng

### Test Case 3: Random Colors
**Setup:**
1. Tạo chat với nhiều users khác nhau không có avatar
2. Tên bắt đầu bằng các chữ cái khác nhau

**Expected:**
- Mỗi user có màu background khác nhau
- Màu được tính dựa trên `name.charCodeAt(0) % colors.length`
- Consistent color cho cùng một user

**Steps:**
1. Test với users: "An", "Bình", "Cường", "Dũng"
2. Kiểm tra màu khác nhau cho mỗi user
3. Kiểm tra màu consistent khi vào lại

### Test Case 4: Transition to Messages
**Setup:**
1. Bắt đầu với empty state
2. Gửi tin nhắn đầu tiên

**Expected:**
- Empty state biến mất
- Tin nhắn hiển thị bình thường
- Không có glitch hoặc animation lỗi

**Steps:**
1. Vào chat room empty
2. Gửi tin nhắn "Hello"
3. Kiểm tra transition smooth

## 🎨 Design Specs

### Layout
```
┌─────────────────────────┐
│                         │
│                         │
│       ┌─────────┐       │
│       │         │       │  ← Avatar (120x120)
│       │    A    │       │    hoặc FastImage
│       │         │       │
│       └─────────┘       │
│                         │
│   Hãy bắt đầu trò chuyện │  ← Text (18px)
│                         │
│                         │
└─────────────────────────┘
```

### Colors
- **Background**: `ColorThemes.light.neutral_absolute_background_color`
- **Text**: `ColorThemes.light.neutral_text_secondary_color`
- **Avatar fallback**: Random từ 10 màu định sẵn
- **Avatar text**: White (#FFFFFF)

### Typography
- **Avatar text**: 48px, bold, white
- **Message text**: 18px, medium weight, secondary color

## 🔧 Implementation Details

### Files Modified
- `src/modules/chat/screens/ChatRoomScreen.tsx`

### Key Functions
```typescript
// Generate random color based on name
const getRandomAvatarColor = (name: string) => {
  const colors = [...];
  const index = name.charCodeAt(0) % colors.length;
  return colors[index];
};

// Render empty state
const renderEmptyState = () => {
  const roomName = room.Name || room.name || 'Người dùng';
  const roomAvatar = room.Avatar;
  const avatarColor = getRandomAvatarColor(roomName);
  // ... render logic
};
```

### Styles Added
```typescript
emptyStateContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' }
emptyStateContent: { alignItems: 'center', justifyContent: 'center' }
emptyStateAvatar: { width: 120, height: 120, borderRadius: 60 }
emptyStateAvatarDefault: { justifyContent: 'center', alignItems: 'center' }
emptyStateAvatarText: { fontSize: 48, fontWeight: 'bold', color: 'white' }
emptyStateText: { fontSize: 18, color: secondary, textAlign: 'center' }
```

## 🚀 Manual Testing

### Quick Test Steps
1. **Open app** → Login
2. **Go to Chat tab** → Danh bạ
3. **Select contact** → Nhấn icon chat 💬
4. **Verify empty state**:
   - Avatar hiển thị đúng (có/không có ảnh)
   - Text "Hãy bắt đầu trò chuyện"
   - Layout centered và đẹp
5. **Send first message** → Verify transition

### Edge Cases
- **Tên rỗng**: Fallback to "Người dùng"
- **Tên 1 ký tự**: Hiển thị ký tự đó
- **Tên Unicode**: Lấy ký tự đầu đúng
- **Avatar URL lỗi**: Fallback to default avatar

## 📱 Screenshots Checklist

Cần chụp screenshots cho:
- [ ] Empty state với avatar có ảnh
- [ ] Empty state với avatar fallback (nhiều màu khác nhau)
- [ ] Transition từ empty state sang có tin nhắn
- [ ] Responsive trên các kích thước màn hình khác nhau

## ✅ Acceptance Criteria

- [x] Avatar hiển thị 120x120px
- [x] Text "Hãy bắt đầu trò chuyện" bên dưới avatar
- [x] Fallback avatar với chữ cái đầu và màu random
- [x] Layout centered và responsive
- [x] Smooth transition khi có tin nhắn đầu tiên
- [x] Consistent color cho cùng user
- [x] Proper error handling cho edge cases

Empty state đã sẵn sàng cho production! 🎉
